<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>

<!-- 首页概览 -->
<li><a href="<c:url value='/admin/library' />"><i class="fa fa-home"></i> <span>首页概览</span></a></li>

<li class="menu-list" id="menu_library"><a href="#"><i class="fa fa-book"></i> <span>图书管理</span></a>
    <ul class="sub-menu-list">
        <sec:authorize access="hasAnyRole('ADMIN','OPERATION','LIBRARIAN')">
            <li id="librariesList"><a href="<c:url value='/admin/library/libraries' />">图书馆列表</a></li>
        </sec:authorize>
        <li id="libraryBookList"><a href="<c:url value='/admin/library/books' />">馆藏书籍管理</a></li>
        <li id="libraryBookBorrow"><a href="<c:url value="/admin/library/borrow" />">借阅和归还管理</a></li>
        <li id="libraryBookReserve"><a href="<c:url value="/admin/library/reserve" />">预约管理</a></li>
        <li id="librarySettings"><a href="<c:url value="/admin/library/settings" />">借阅规则</a></li>
        <sec:authorize access="hasRole('ADMIN')">
            <li id="libraryLibrarians"><a href="<c:url value='/admin/library/librarians' />">图书馆管理员</a></li>
        </sec:authorize>
    </ul>
</li>

<li class="menu-list" id="menu_readers"><a href="#"><i class="fa fa-users"></i> <span>读者管理</span></a>
    <ul class="sub-menu-list">
        <li id="readerList"><a href="<c:url value="/admin/library/readers" />">读者列表</a></li>
        <li id="readerStatistics"><a href="<c:url value="/admin/library/readerStats" />">借阅统计</a></li>
        <li id="readerHistory"><a href="<c:url value="/admin/library/readerHistory" />">借阅历史</a></li>
    </ul>
</li>

<li class="menu-list" id="menu_reports"><a href="#"><i class="fa fa-chart-bar"></i> <span>统计报表</span></a>
    <ul class="sub-menu-list">
        <li id="borrowReport"><a href="<c:url value="/admin/library/borrowReport" />">借阅报表</a></li>
        <li id="bookUsageReport"><a href="<c:url value="/admin/library/bookUsage" />">图书利用率</a></li>
        <li id="overdueReport"><a href="<c:url value="/admin/library/overdue" />">逾期报表</a></li>
    </ul>
</li>

<li class="menu-list" id="menu_system"><a href="#"><i class="fa fa-cog"></i> <span>系统管理</span></a>
    <ul class="sub-menu-list">
        <li id="passwd"><a href="<c:url value="/user/updatePwd" />"><spring:message code="passwd.update"/></a></li>
    </ul>
</li>

<li class="menu-list" id="menu_lang"><a href="#"><i class="fa fa-user"></i> <span>语言/language</span></a>
    <ul class="sub-menu-list">
        <c:choose>
            <c:when test="${pageContext.response.locale == 'zh_CN'}">
                <li><a href="<c:url value="/admin/library" />?locale=zh_HK"><i class="fa fa-stack-exchange"></i> <span>切換繁體 </span></a></li>
                <li><a href="<c:url value="/admin/library" />?locale=en_US"><i class="fa fa-stack-exchange"></i> <span>English </span></a></li>
            </c:when>
            <c:when test="${pageContext.response.locale == 'zh_HK'}">
                <li><a href="<c:url value="/admin/library" />?locale=zh_CN"><i class="fa fa-stack-exchange"></i> <span>切换简体</span></a></li>
                <li><a href="<c:url value="/admin/library" />?locale=en_US"><i class="fa fa-stack-exchange"></i> <span>English </span></a></li>
            </c:when>
            <c:otherwise>
                <li><a href="<c:url value="/admin/library" />?locale=zh_CN"><i class="fa fa-stack-exchange"></i> <span>切换简体</span></a></li>
                <li><a href="<c:url value="/admin/library" />?locale=zh_HK"><i class="fa fa-stack-exchange"></i> <span>切換繁體 </span></a></li>
            </c:otherwise>
        </c:choose>
    </ul>
</li>
