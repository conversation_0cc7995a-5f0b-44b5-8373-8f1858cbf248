<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.LibUserCustomMapper">

    <resultMap id="LibrarianViewMap" type="com.aaron.spring.model.view.LibLibrarianView">
        <id column="lib_user_id" property="libUserId" />
        <result column="user_email" property="userEmail" />
        <result column="library_id" property="libraryId" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="library_name" property="libraryName" />
        <result column="library_key" property="libraryKey" />
        <result column="firstname" property="firstname" />
        <result column="display_name" property="displayName" />
    </resultMap>

    <sql id="BaseFromAndWhere">
        FROM lib_user lu
        LEFT JOIN lib_library ll ON lu.library_id = ll.library_id
        LEFT JOIN oc_customer oc ON lu.user_email = oc.email
        WHERE lu.is_deleted = 0
          AND lu.status IN (1,2)
          AND ll.is_deleted = 0
          AND ll.status = 1
        <if test="keyword != null and keyword != ''">
            AND (lu.user_email LIKE #{keyword}
                 OR ll.name LIKE #{keyword}
                 OR oc.firstname LIKE #{keyword})
        </if>
    </sql>

        <select id="selectLibrarians" resultMap="LibrarianViewMap">
        SELECT
            lu.lib_user_id,
            lu.user_email,
            lu.library_id,
            lu.status,
            lu.created_at,
            ll.name AS library_name,
            ll.`key` AS library_key,
            oc.firstname,
            lu.display_name
        <include refid="BaseFromAndWhere"/>
        ORDER BY ll.name ASC, lu.created_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countLibrarians" resultType="int">
        SELECT COUNT(*)
        <include refid="BaseFromAndWhere"/>
    </select>

    <!-- 查询管理员（限制在某用户可管理的图书馆范围内） -->
    <select id="selectLibrariansByUserEmail" resultMap="LibrarianViewMap">
        SELECT
            lu.lib_user_id,
            lu.user_email,
            lu.library_id,
            lu.status,
            lu.created_at,
            ll.name AS library_name,
            ll.`key` AS library_key,
            oc.firstname,
            lu.display_name
        FROM lib_user lu
        LEFT JOIN lib_library ll ON lu.library_id = ll.library_id
        LEFT JOIN oc_customer oc ON lu.user_email = oc.email
        INNER JOIN (
            SELECT DISTINCT library_id FROM lib_user
            WHERE is_deleted = 0 AND status = 2 AND user_email = #{userEmail}
        ) AS scope ON scope.library_id = lu.library_id
        WHERE lu.is_deleted = 0
          AND lu.status IN (1,2)
          AND ll.is_deleted = 0
          AND ll.status = 1
        <if test="keyword != null and keyword != ''">
            AND (
                lu.user_email LIKE #{keyword}
                OR ll.name LIKE #{keyword}
                OR oc.firstname LIKE #{keyword}
            )
        </if>
        ORDER BY ll.name ASC, lu.created_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countLibrariansByUserEmail" resultType="int">
        SELECT COUNT(1)
        FROM lib_user lu
        LEFT JOIN lib_library ll ON lu.library_id = ll.library_id
        LEFT JOIN oc_customer oc ON lu.user_email = oc.email
        INNER JOIN (
            SELECT DISTINCT library_id FROM lib_user
            WHERE is_deleted = 0 AND status = 2 AND user_email = #{userEmail}
        ) AS scope ON scope.library_id = lu.library_id
        WHERE lu.is_deleted = 0
          AND lu.status IN (1,2)
          AND ll.is_deleted = 0
          AND ll.status = 1
        <if test="keyword != null and keyword != ''">
            AND (
                lu.user_email LIKE #{keyword}
                OR ll.name LIKE #{keyword}
                OR oc.firstname LIKE #{keyword}
            )
        </if>
    </select>

    <!-- 查询某邮箱可管理的启用图书馆列表（已确认加入 status=2） -->
    <select id="selectEnabledLibrariesByUserEmail" resultType="com.aaron.spring.model.LibLibrary">
        SELECT
            ll.library_id,
            ll.name,
            ll.description,
            ll.`key`,
            ll.status,
            ll.remark,
            ll.is_deleted,
            ll.max_borrow_books,
            ll.default_loan_days,
            ll.created_at
        FROM lib_user lu
        INNER JOIN lib_library ll ON lu.library_id = ll.library_id
        WHERE lu.is_deleted = 0
          AND lu.status = 2
          AND ll.is_deleted = 0
          AND ll.status = 1
          AND lu.user_email = #{userEmail}
        GROUP BY ll.library_id
        ORDER BY ll.name ASC
    </select>

    <!-- 按图书馆ID查询该馆管理员（已确认 status=2） -->
    <select id="selectLibrariansByLibraryId" resultMap="LibrarianViewMap">
        SELECT
            lu.lib_user_id,
            lu.user_email,
            lu.library_id,
            lu.status,
            lu.created_at,
            ll.name AS library_name,
            ll.`key` AS library_key,
            oc.firstname,
            lu.display_name
        FROM lib_user lu
        LEFT JOIN lib_library ll ON lu.library_id = ll.library_id
        LEFT JOIN oc_customer oc ON lu.user_email = oc.email
        WHERE lu.is_deleted = 0
          AND lu.status = 2
          AND lu.library_id = #{libraryId}
        ORDER BY oc.firstname ASC, lu.user_email ASC
    </select>

    <!-- 查询单个图书馆管理员详细信息 -->
    <select id="selectLibrarianDetail" resultMap="LibrarianViewMap">
        SELECT
            lu.lib_user_id,
            lu.user_email,
            lu.library_id,
            lu.status,
            lu.created_at,
            ll.name AS library_name,
            ll.`key` AS library_key,
            oc.firstname,
            lu.display_name
        FROM lib_user lu
        LEFT JOIN lib_library ll ON lu.library_id = ll.library_id
        LEFT JOIN oc_customer oc ON lu.user_email = oc.email
        WHERE lu.is_deleted = 0
          AND lu.lib_user_id = #{libUserId}
    </select>
</mapper>
