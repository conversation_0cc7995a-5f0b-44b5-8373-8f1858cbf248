<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="csrf-token" content="${_csrf.token}">
    <title>新建图书馆 - <spring:message code="main.title"/></title>
    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <script src="<c:url value='/js/jquery.min.js'/>"></script>
    <script>
        function checkKeyUnique(){
            var k = $('#key').val(); if (!k) return;
            $.get('<c:url value="/admin/library/api/libraries/check-key"/>', {key:k}).done(function(resp){
                if (resp && resp.data && resp.data.exists){
                    $('#keyHelp').text('标识码已存在').css('color','#e67e22');
                } else { $('#keyHelp').text(''); }
            });
        }
        function save(){
            var params = {
                name: $('#name').val(),
                key: $('#key').val(),
                description: $('#description').val(),
                max_borrow_books: $('#maxBorrowBooks').val(),
                default_loan_days: $('#defaultLoanDays').val()
            };
            if ($('#status').length){ params.status = $('#status').val(); }
            if ($('#remark').length){ params.remark = $('#remark').val(); }
            $.jpost('<c:url value="/admin/library/api/libraries"/>', params).then(function(resp){
                if (resp && (resp.code==='T'||resp.code===200||resp.code===0)){
                    showNotification('创建成功','success');
                    window.location.href = '<c:url value="/admin/library/libraries"/>';
                } else {
                    showNotification(resp.message||'创建失败','error');
                }
            }).catch(function(){
                showNotification('网络错误','error');
            });
        }

        function showNotification(message, type) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
                message +
                '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                '<span aria-hidden="true">&times;</span>' +
                '</button>' +
                '</div>');

            $('body').append(notification);

            // 自动移除通知
            setTimeout(function() {
                notification.alert('close');
            }, 3000);
        }
    </script>
    <sec:authorize access="hasRole('ADMIN')"><script>window.__isAdmin = true;</script></sec:authorize>
    <sec:authorize access="!hasRole('ADMIN')"><script>window.__isAdmin = false;</script></sec:authorize>
</head>
<body class="sticky-header">
<section>
    <jsp:include page="../adminLeft.jsp"/>
    <div class="main-content">
        <div class="header-section">
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <jsp:include page="header_library_switcher.jsp"/>
        </div>
        <div class="page-heading">
            <h3>新建图书馆</h3>
            <ul class="breadcrumb">
                <li><a href="#"><spring:message code="menu.index"/></a></li>
                <li><a href="<c:url value='/admin/library' />">图书馆后台</a></li>
                <li class="active">新建</li>
            </ul>
        </div>
        <div class="wrapper">
            <section class="panel">
                <header class="panel-heading">基础信息</header>
                <div class="panel-body">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">图书馆名称<span style="color:#e74c3c;"> *</span></label>
                            <div class="col-sm-6"><input id="name" type="text" class="form-control" maxlength="255" placeholder="T学院图书馆"></div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">标识码</label>
                            <div class="col-sm-6">
                                <input id="key" type="text" class="form-control" maxlength="50" placeholder="如 tti_seminary（仅小写字母/数字/_/-）" onblur="checkKeyUnique()">
                                <div id="keyHelp" class="help-block">创建后不可修改</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">最大借阅数量</label>
                            <div class="col-sm-3">
                                <input id="maxBorrowBooks" type="number" min="1" max="100" class="form-control" value="10" placeholder="每人最多可同时借阅的图书数量">
                                <div class="help-block">每人最多可同时借阅的图书数量</div>
                            </div>
                            <label class="col-sm-2 control-label">默认借阅天数</label>
                            <div class="col-sm-3">
                                <input id="defaultLoanDays" type="number" min="1" max="90" class="form-control" value="14" placeholder="图书的默认借阅期限（天）">
                                <div class="help-block">图书的默认借阅期限（天）</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">描述</label>
                            <div class="col-sm-8"><textarea id="description" class="form-control" rows="4" maxlength="2000" placeholder="请输入图书馆描述（可选）"></textarea></div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">状态</label>
                            <div class="col-sm-3">
                                <select id="status" class="form-control"><option value="1" selected>启用</option><option value="0">停用</option></select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">备注</label>
                            <div class="col-sm-8"><textarea id="remark" class="form-control" rows="3" maxlength="500" placeholder="请输入备注（可选）"></textarea></div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-8">
                                <button class="btn btn-primary" onclick="save()">保存</button>
                                <a class="btn btn-default" href="<c:url value='/admin/library/libraries'/>">返回</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <jsp:include page="../footer_show.jsp"/>
    </div>
</section>

<!-- 引入必要的JavaScript -->
<jsp:include page="../footer_js.jsp"/>

</body>
</html>
<script>
$(function(){
    if (typeof window.__isAdmin !== 'undefined' && !window.__isAdmin){
        if ($('#status').length){ $('#status').closest('.form-group').remove(); }
        if ($('#remark').length){ $('#remark').closest('.form-group').remove(); }
    }
});
</script>
