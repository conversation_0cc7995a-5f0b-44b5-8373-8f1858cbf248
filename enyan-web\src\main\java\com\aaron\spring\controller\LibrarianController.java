package com.aaron.spring.controller;

import com.aaron.spring.model.*;
import com.aaron.spring.service.*;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.spring.service.page.Pages;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import com.aaron.spring.mapper.custom.LibUserCustomMapper;
import com.aaron.spring.mapper.LibUserMapper;
import com.aaron.spring.mapper.LibBookMapper;
import com.aaron.spring.mapper.LibBorrowMapper;
import com.aaron.spring.model.view.LibLibrarianView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.*;
import io.swagger.v3.oas.annotations.Operation;
import com.aaron.spring.common.EBookConstant;

/**
     * 图书管理员控制器
     * 
     * @Author: Aaron Hao
     * @Date: Created in 2025/09/02
     */
@Slf4j
@Controller
@RequestMapping("/admin/library")
@io.swagger.annotations.Api(tags = "Admin Library 管理后台接口")
public class LibrarianController extends UserController {


    @Autowired
    private AuthUserService authUserService;

    @Autowired
    private LibLibraryService libLibraryService;

    @Autowired
    private LibBookListService libBookListService;

    @Autowired
    private LibUserCustomMapper libUserCustomMapper;

    @Autowired
    private LibUserMapper libUserMapper;

    @Autowired
    private LibBookMapper libBookMapper;

    @Autowired
    private LibBorrowMapper libBorrowMapper;

    @Autowired
    private EnyanBookService enyanBookService;

    @Autowired
    private LibBookService libBookService;

    /**
     * 图书馆管理首页
     */
    @RequestMapping(value = {"", "/"}, method = RequestMethod.GET)
    public String index(HttpServletRequest request, ModelMap modelMap) {
        log.debug("访问图书馆管理首页");

        // 调试用户权限
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
//        System.out.println("DEBUG LibrarianController: Authentication = " + auth);
        if (auth != null) {
//            System.out.println("DEBUG LibrarianController: Principal = " + auth.getPrincipal());
//            System.out.println("DEBUG LibrarianController: Authorities = " + auth.getAuthorities());
            for (GrantedAuthority authority : auth.getAuthorities()) {
//                System.out.println("DEBUG LibrarianController: Authority = " + authority.getAuthority());
            }
        }

        // 模拟统计数据 - 实际应用中应从数据库获取
        modelMap.addAttribute("totalBooks", 1234);
        modelMap.addAttribute("totalReaders", 567);
        modelMap.addAttribute("currentBorrows", 89);
        modelMap.addAttribute("overdueBooks", 12);
        modelMap.addAttribute("todayBorrows", 15);
        modelMap.addAttribute("todayReturns", 8);

        return "admin/library/index";
    }

    /**
     * 图书目录管理
     */
    @RequestMapping("/books")
    public String books(ModelMap modelMap) {
        return "admin/library/books";
    }

    /**
     * 借阅管理
     */
    @RequestMapping("/borrow")
    public String borrow(ModelMap modelMap) {
        return "admin/library/borrow";
    }

    /**
     * 归还管理
     */
    @RequestMapping("/return")
    public String returnBooks(ModelMap modelMap) {
        return "admin/library/return";
    }

    /**
     * 预约管理
     */
    @RequestMapping("/reserve")
    public String reserve(ModelMap modelMap) {
        return "admin/library/reserve";
    }

    /**
     * 读者列表
     */
    @RequestMapping("/readers")
    public String readers(ModelMap modelMap) {
        return "admin/library/readers";
    }

    /**
     * 借阅统计
     */
    @RequestMapping("/readerStats")
    public String readerStats(ModelMap modelMap) {
        return "admin/library/readerStats";
    }

    /**
     * 借阅历史
     */
    @RequestMapping("/readerHistory")
    public String readerHistory(ModelMap modelMap) {
        return "admin/library/readerHistory";
    }

    /**
     * 借阅报表
     */
    @RequestMapping("/borrowReport")
    public String borrowReport(ModelMap modelMap) {
        return "admin/library/borrowReport";
    }

    /**
     * 图书利用率
     */
    @RequestMapping("/bookUsage")
    public String bookUsage(ModelMap modelMap) {
        return "admin/library/bookUsage";
    }

    /**
     * 逾期报表
     */
    @RequestMapping("/overdue")
    public String overdue(ModelMap modelMap) {
        return "admin/library/overdue";
    }

    /**
     * 借阅规则设置
     */
    @RequestMapping("/settings")
    public String settings(ModelMap modelMap) {
        return "admin/library/settings";
    }

    /**
     * 图书馆编辑页（创建/编辑共用）
     */
    @RequestMapping("/library-edit")
    public String libraryEdit(HttpServletRequest request, ModelMap modelMap) {
        String idStr = request.getParameter("id");

        if (StringUtils.isNotBlank(idStr)) {
            try {
                Long libraryId = Long.parseLong(idStr.trim());

                // 视图层权限校验：
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                Collection<? extends GrantedAuthority> authorities = authentication != null ? authentication.getAuthorities() : null;
                String userEmail = authentication != null ? authentication.getName() : null;
                if (userEmail != null) userEmail = userEmail.trim().toLowerCase();
                boolean isSystemAdmin = authorities != null && authorities.stream().anyMatch(a -> "ROLE_ADMIN".equals(a.getAuthority()));
                boolean isOperationAdmin = authorities != null && authorities.stream().anyMatch(a -> "ROLE_OPERATION".equals(a.getAuthority()));
                boolean isLibrarian = authorities != null && authorities.stream().anyMatch(a -> "ROLE_LIBRARIAN".equals(a.getAuthority()));

                if (!(isSystemAdmin || isOperationAdmin)) {
                    if (isLibrarian && userEmail != null) {
                        // 馆员必须是该馆管理员，且建议与当前切换馆一致
                        int count = libUserMapper.countConfirmedByUserAndLibrary(userEmail, libraryId);
                        if (count <= 0) {
                            return "redirect:/admin/library/libraries";
                        }
                        HttpSession session = request.getSession(false);
                        if (session != null) {
                            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");
                            if (currentLibraryId != null && !libraryId.equals(currentLibraryId)) {
                                return "redirect:/admin/library/libraries";
                            }
                        }
                    } else {
                        return "redirect:/admin/library/libraries";
                    }
                }
                
                // 获取图书馆详情并传递到前端
                ExecuteResult<LibLibrary> result = libLibraryService.queryRecordByPrimaryKey(libraryId);
                
                if (result != null && result.isSuccess() && result.getResult() != null) {
                    LibLibrary library = result.getResult();
                    modelMap.addAttribute("library", library);
                    modelMap.addAttribute("libraryId", libraryId);
                } else {
                    // 图书馆不存在，重定向到列表页
                    return "redirect:/admin/library/libraries";
                }
            } catch (NumberFormatException e) {
                // ID格式错误，重定向到列表页
                return "redirect:/admin/library/libraries";
            }
        }
        return "admin/library/library-edit";
    }

    /**
     * 新建图书馆页
     */
    @RequestMapping("/library-create")
    public String libraryCreate(ModelMap modelMap) {
        // 仅系统管理员/运营可进入
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Collection<? extends GrantedAuthority> authorities = authentication != null ? authentication.getAuthorities() : null;
        boolean allowed = authorities != null && authorities.stream().anyMatch(a ->
                "ROLE_ADMIN".equals(a.getAuthority()) || "ROLE_OPERATION".equals(a.getAuthority())
        );
        if (!allowed) {
            return "redirect:/admin/library/libraries";
        }
        return "admin/library/library-create";
    }

    /**
     * 书单列表页
     */
    @RequestMapping("/book-lists")
    public String bookLists(ModelMap modelMap) {
        return "admin/library/book-lists";
    }

    /**
     * 图书馆列表页
     */
    @RequestMapping("/libraries")
    public String libraries(HttpServletRequest req, ModelMap modelMap) {
        String pageNoStr = req.getParameter("pageNo");
        String name = req.getParameter("name");
        String key = req.getParameter("key");
        String statusStr = req.getParameter("status");

        int pageNo = 1;
        int pageSize = 20;
        if (pageNoStr != null && pageNoStr.trim().length() > 0) {
            try { pageNo = Integer.parseInt(pageNoStr); } catch (Exception ignored) {}
        }

        // 获取当前用户角色/账号
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        java.util.List<String> currentRoles = new java.util.ArrayList<>();
        if (authentication != null && authentication.getAuthorities() != null) {
            for (GrantedAuthority authority : authentication.getAuthorities()) {
                if (authority != null && authority.getAuthority() != null) {
                    currentRoles.add(authority.getAuthority());
                }
            }
        }
        String userEmail = authentication != null ? authentication.getName() : null;
        if (userEmail != null) userEmail = userEmail.trim().toLowerCase();
        boolean isSystemAdmin = currentRoles.contains("ROLE_ADMIN");
        boolean isOperationAdmin = currentRoles.contains("ROLE_OPERATION");
        boolean isLibrarian = currentRoles.contains("ROLE_LIBRARIAN");

        java.util.List<LibLibrary> list = new java.util.ArrayList<>();
        long totalRecord = 0;
        // 馆员可管理ID串，供前端判断按钮显示
        String managedLibraryIdStr = ""; // 形如 ",1,2,3,"
        java.util.Map<Long, Boolean> canEditMap = new java.util.HashMap<>();

        if (isSystemAdmin || isOperationAdmin) {
            // 管理员：按条件分页查询全部
            LibLibrary cond = new LibLibrary();
            cond.setIsDeleted(0);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) cond.setName(name.trim());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(statusStr)) {
                try { cond.setStatus(Integer.parseInt(statusStr.trim())); } catch (Exception ignored) {}
            }
            Page<LibLibrary> page = new Page<>(pageNo, pageSize);
            page = libLibraryService.queryRecords(page, cond);
            list = page != null ? page.getRecords() : new java.util.ArrayList<>();
            totalRecord = page != null ? page.getTotalRecord() : 0;

            // 管理员可编辑列表中所有图书馆
            for (LibLibrary lib : list) { if (lib != null && lib.getLibraryId() != null) canEditMap.put(lib.getLibraryId(), Boolean.TRUE); }

            // 仅在页面端补充 key 等值过滤
            if (org.apache.commons.lang3.StringUtils.isNotBlank(key)) {
                String k = key.trim();
                java.util.List<LibLibrary> filtered = new java.util.ArrayList<>();
                for (LibLibrary r : list) { if (r != null && k.equals(r.getKey())) filtered.add(r); }
                list = filtered;
                totalRecord = filtered.size();
            }

        } else if (isLibrarian && org.apache.commons.lang3.StringUtils.isNotBlank(userEmail)) {
            // 图书馆管理员：仅查看自己管理的图书馆，手动过滤+分页
            java.util.List<LibLibrary> managed = libUserCustomMapper.selectEnabledLibrariesByUserEmail(userEmail);
            // 生成前端判断用的ID串
            if (managed != null && !managed.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                sb.append(',');
                for (LibLibrary m : managed) { if (m != null && m.getLibraryId() != null) { sb.append(m.getLibraryId()).append(','); canEditMap.put(m.getLibraryId(), Boolean.TRUE);} }
                managedLibraryIdStr = sb.toString();
            }
            java.util.List<LibLibrary> filtered = new java.util.ArrayList<>();
            for (LibLibrary library : managed) {
                if (library == null) continue;
                boolean match = true;
                if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
                    if (library.getName() == null || !library.getName().contains(name.trim())) match = false;
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(key)) {
                    if (!key.trim().equals(library.getKey())) match = false;
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(statusStr)) {
                    try {
                        Integer st = Integer.parseInt(statusStr.trim());
                        if (library.getStatus() == null || !st.equals(library.getStatus())) match = false;
                    } catch (Exception ignored) {}
                }
                if (match) filtered.add(library);
            }
            totalRecord = filtered.size();
            int start = Math.max(0, (pageNo - 1) * pageSize);
            int end = Math.min(start + pageSize, filtered.size());
            if (start < end) {
                list = filtered.subList(start, end);
            } else {
                list = new ArrayList<>();
            }
        } else {
            // 其他角色无权访问列表，返回空列表（或可跳转403页）
            list = new ArrayList<>();
            totalRecord = 0;
        }

        Pages pages = new Pages(pageNo,
                (int) totalRecord, pageSize);
        Map<String, Object> queryParams = new HashMap<>();
        if (StringUtils.isNotBlank(name)) { queryParams.put("name", name); }
        if (StringUtils.isNotBlank(key)) { queryParams.put("key", key); }
        if (StringUtils.isNotBlank(statusStr)) { queryParams.put("status", statusStr); }
        pages.excutePageLand(queryParams);

        modelMap.addAttribute("name", name);
        modelMap.addAttribute("key", key);
        modelMap.addAttribute("status", statusStr);
        // 组装附加信息：管理员与馆藏图书数量
        Map<Long, List<LibLibrarianView>> adminMap = new HashMap<>();
        Map<Long, Long> bookCountMap = new HashMap<>();
        for (LibLibrary r : list) {
            if (r == null || r.getLibraryId() == null) continue;
            Long lid = r.getLibraryId();
            try {
                List<LibLibrarianView> admins = libUserCustomMapper.selectLibrariansByLibraryId(lid);
                adminMap.put(lid, admins == null ? Collections.emptyList() : admins);
            } catch (Exception ignored) { adminMap.put(lid, Collections.emptyList()); }
            try {
                LibBookExample ex = new LibBookExample();
                LibBookExample.Criteria bc = ex.createCriteria();
                bc.andLibraryIdEqualTo(lid);
                bc.andStatusEqualTo(1);
                bc.andIsDeletedEqualTo(0);
                long cnt = libBookMapper.countByExample(ex);
                bookCountMap.put(lid, cnt);
            } catch (Exception ignored) { bookCountMap.put(lid, 0L); }
        }

        modelMap.addAttribute("list", list);
        modelMap.addAttribute("adminMap", adminMap);
        modelMap.addAttribute("bookCountMap", bookCountMap);
        modelMap.addAttribute("currentUserRoles", currentRoles);
        modelMap.addAttribute("currentUserRolesString", String.join(",", currentRoles));
        modelMap.addAttribute("pageLand", pages.getPageLand());
        modelMap.addAttribute("currentPageNo", pageNo);
        modelMap.addAttribute("totalRecord", totalRecord);
        modelMap.addAttribute("managedLibraryIdStr", managedLibraryIdStr);
        modelMap.addAttribute("canEditMap", canEditMap);
        return "admin/library/libraries2";
    }

    /**
     * 图书馆管理员列表页（仅系统管理员模式显示入口）
     * 使用与其他后台一致的分页风格（${pageLand}），支持 keyword 查询。
     */
    @RequestMapping("/librarians")
    public String librarians(HttpServletRequest req, ModelMap modelMap) {
        // 分页参数：与其他后台页面保持一致
        String pageNoStr = req.getParameter("pageNo");
        String keyword = req.getParameter("keyword");

        int pageNo = 1;
        int pageSize = 20;
        if (pageNoStr != null && pageNoStr.trim().length() > 0) {
            try { pageNo = Integer.parseInt(pageNoStr); } catch (Exception ignored) {}
        }

        // 权限识别
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String userEmail = authentication != null ? authentication.getName() : null;
        if (userEmail != null) userEmail = userEmail.trim().toLowerCase();
        List<String> currentRoles = new ArrayList<>();
        if (authentication != null && authentication.getAuthorities() != null) {
            for (GrantedAuthority authority : authentication.getAuthorities()) {
                if (authority != null && authority.getAuthority() != null) {
                    currentRoles.add(authority.getAuthority());
                }
            }
        }
        boolean isSystemAdmin = currentRoles.contains("ROLE_ADMIN");
        boolean isOperationAdmin = currentRoles.contains("ROLE_OPERATION");
        boolean isLibrarian = currentRoles.contains("ROLE_LIBRARIAN");

        String kwLike = StringUtils.isBlank(keyword) ? null : "%" + keyword.trim() + "%";
        int total;
        int offset = Math.max(0, (pageNo - 1) * pageSize);
        List<LibLibrarianView> records;

        if (isSystemAdmin || isOperationAdmin) {
            // 系统管理员/运营：查看全部
            total = libUserCustomMapper.countLibrarians(kwLike);
            records = libUserCustomMapper.selectLibrarians(kwLike, offset, pageSize);
        } else if (isLibrarian && StringUtils.isNotBlank(userEmail)) {
            // 图书馆管理员：仅查看自己馆的管理员
            total = libUserCustomMapper.countLibrariansByUserEmail(kwLike, userEmail);
            records = libUserCustomMapper.selectLibrariansByUserEmail(kwLike, userEmail, offset, pageSize);
        } else {
            // 无权访问，返回空
            total = 0;
            records = new ArrayList<>();
        }

        // 构建分页条
        Pages pages = new Pages(pageNo, Math.max(0, total), pageSize);
        Map<String, Object> queryParams = new HashMap<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryParams.put("keyword", keyword);
        }
        pages.excutePageLand(queryParams);

        // 分组：按图书馆名称聚合，便于在页面合并单元格
        LinkedHashMap<String, List<LibLibrarianView>> grouped = new LinkedHashMap<>();
        for (LibLibrarianView r : records) {
            String key = r.getLibraryName() == null ? "" : r.getLibraryName();
            grouped.computeIfAbsent(key, k -> new ArrayList<>()).add(r);
        }

        modelMap.addAttribute("keyword", keyword);
        modelMap.addAttribute("grouped", grouped);
        modelMap.addAttribute("list", records);
        modelMap.addAttribute("pageLand", pages.getPageLand());
        modelMap.addAttribute("currentPageNo", pageNo);
        modelMap.addAttribute("totalRecord", Math.max(0, total));
        return "admin/library/librarians";
    }
    
    // ==================== 管理员维护 API ====================

    /** 添加图书馆管理员（ADMIN/OPERATION） */
    @RequestMapping(value = "/api/librarians", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-添加图书馆管理员")
    public ResponseEntity<Map<String, Object>> addLibrarian(
            @RequestParam("libraryId") Long libraryId,
            @RequestParam("userEmail") String userEmail,
            @RequestParam(value = "displayName", required = false) String displayName,
            @RequestParam(value = "password", required = false) String password,
            @RequestParam(value = "remark", required = false) String remark,
            HttpServletRequest request) {
        Map<String, Object> resp = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            boolean allowed = authorities.stream().anyMatch(a ->
                    "ROLE_ADMIN".equals(a.getAuthority()) || "ROLE_OPERATION".equals(a.getAuthority())
            );
            if (!allowed) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限");
                return ResponseEntity.ok(resp);
            }
            if (libraryId == null || libraryId <= 0 || StringUtils.isBlank(userEmail)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "参数不完整");
                return ResponseEntity.ok(resp);
            }

            // 账号规范化：转小写并去空白字符
            userEmail = userEmail.trim().toLowerCase();
            
            // 图书馆存在且启用
            LibLibrary lib = libLibraryService.queryRecordByPrimaryKey(libraryId).getResult();
            if (lib == null || lib.getStatus() == null || lib.getStatus() != 1) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "图书馆不存在或已禁用");
                return ResponseEntity.ok(resp);
            }
            
            // 完善重复检测：使用规范化后的账号检测已存在的活动记录
            int exists = libUserMapper.countActiveByUserAndLibrary(userEmail, libraryId);
            if (exists > 0) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "该账号已是该图书馆管理员");
                return ResponseEntity.ok(resp);
            }
            
            // 集成AuthUserService：确保oc_customer中存在该用户记录
            try {
                // 先检查用户是否已存在 - 优先通过邮箱查找，再通过用户名查找
                ExecuteResult<AuthUser> existingUser = authUserService.getUserByEmail(userEmail);
                if (existingUser == null || !existingUser.isSuccess() || existingUser.getResult() == null) {
                    // 尝试通过用户名查找
                    existingUser = authUserService.getUserByName(userEmail);
                }

                if (existingUser == null || !existingUser.isSuccess() || existingUser.getResult() == null) {
                    // 用户不存在，自动注册为图书馆人员
                    AuthUser newUser = new AuthUser();
                    boolean isEmail = userEmail.contains("@");
                    if (isEmail) {
                        newUser.setUserName(userEmail.substring(0, userEmail.indexOf("@"))); // 使用邮箱前缀作为用户名
                        newUser.setEmail(userEmail);
                    } else {
                        // 对于非邮箱账号，管理员需要确保输入符合邮箱格式的账号（如***************）
                        newUser.setUserName(userEmail);
                        newUser.setEmail(userEmail);
                    }
                    // 使用自定义密码或默认密码
                    String finalPassword = StringUtils.isNotBlank(password) ? password.trim() : "LibAdmin123!";
                    newUser.setUserPassword(finalPassword);
                    newUser.setRoleType(EBookConstant.RoleDBValue.ROLE_LIBRARIAN_DB_VALUE);

                    ExecuteResult<AuthUser> regResult = authUserService.reg(newUser);
                    if (!regResult.isSuccess()) {
                        resp.put("code", InterfaceContant.ReturnFlag.API_F);
                        resp.put("message", "创建用户账户失败: " + regResult.getErrorMessages());
                        return ResponseEntity.ok(resp);
                    }

                    // 立即激活用户（跳过邮件激活）
                    authUserService.activeUser(userEmail);
                    log.info("自动为图书馆管理员创建用户账户: {}", userEmail);
                } else {
                    // 用户已存在，检查是否需要更新角色类型为图书馆管理员
                    AuthUser existingAuth = existingUser.getResult();
                    if (existingAuth.getRoleType() != EBookConstant.RoleDBValue.ROLE_LIBRARIAN_DB_VALUE) {
                        existingAuth.setRoleType(EBookConstant.RoleDBValue.ROLE_LIBRARIAN_DB_VALUE);
                        authUserService.updateInfo(existingAuth);
                        log.info("更新用户 {} 角色为图书馆管理员", userEmail);
                    }
                }
            } catch (Exception e) {
                log.error("创建或更新用户账户时出错: {}", userEmail, e);
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "创建用户账户失败: " + e.getMessage());
                return ResponseEntity.ok(resp);
            }
            
            // 恢复已删除的记录或插入新记录
            int re = libUserMapper.reactivateAdmin(userEmail, libraryId, remark, displayName);
            if (re == 0) {
                // 插入新记录，状态设为已确认(status=2)
                LibUser rec = new LibUser();
                rec.setUserEmail(userEmail);
                rec.setLibraryId(libraryId);
                rec.setStatus(2); // 已确认加入
                rec.setRemark(remark);
                rec.setDisplayName(displayName); // 设置显示名称
                libUserMapper.insertAdmin(rec);
            }
            resp.put("code", InterfaceContant.ReturnFlag.API_T);
            resp.put("message", "添加成功");
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("添加图书馆管理员失败", e);
            Map<String, Object> err = new HashMap<>();
            err.put("code", InterfaceContant.ReturnFlag.API_F);
            err.put("message", "添加失败: " + e.getMessage());
            return ResponseEntity.ok(err);
        }
    }

    /** 删除图书馆管理员（ADMIN/OPERATION） */
    @RequestMapping(value = "/api/librarians/{libUserId}/delete", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-删除图书馆管理员")
    public ResponseEntity<Map<String, Object>> deleteLibrarian(@PathVariable("libUserId") Long libUserId) {
        Map<String, Object> resp = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            boolean allowed = authorities.stream().anyMatch(a ->
                    "ROLE_ADMIN".equals(a.getAuthority()) || "ROLE_OPERATION".equals(a.getAuthority())
            );
            if (!allowed) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限");
                return ResponseEntity.ok(resp);
            }
            if (libUserId == null || libUserId <= 0) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "参数不完整");
                return ResponseEntity.ok(resp);
            }

            // 获取要删除的管理员记录
            LibUser libUser = libUserMapper.selectByLibUserId(libUserId);
            if (libUser == null) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "记录不存在或已删除");
                return ResponseEntity.ok(resp);
            }

            // 检查是否为该图书馆的唯一管理员
            int activeAdminsCount = libUserMapper.countActiveAdminsByLibrary(libUser.getLibraryId());
            if (activeAdminsCount <= 1) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无法删除：该管理员是此图书馆的最后一个管理员");
                return ResponseEntity.ok(resp);
            }

            // 软删除lib_user记录
            int u = libUserMapper.softDeleteById(libUserId);
            if (u > 0) {
                // 检查该用户是否还管理其他图书馆
                String userEmail = libUser.getUserEmail().trim().toLowerCase();
                int otherLibraryCount = libUserMapper.countActiveLibrariesByUserExcept(userEmail, libUser.getLibraryId());
                
                if (otherLibraryCount == 0) {
                    // 如果该用户不再管理任何图书馆，直接注销该用户
                    try {
//                        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                        String operatorEmail = authentication != null ? authentication.getName() : "system";
                        
                        ExecuteResult<String> revokeResult = authUserService.revokeUser(userEmail, operatorEmail);
                        if (revokeResult != null && revokeResult.isSuccess()) {
                            log.info("用户 {} 不再管理任何图书馆，已自动注销账户", userEmail);
                        } else {
                            log.warn("注销用户失败: {} - {}", userEmail, revokeResult != null ? revokeResult.getErrorMessages() : "未知错误");
                        }
                    } catch (Exception e) {
                        log.warn("注销用户时出错: {}", userEmail, e);
                        // 不影响主要的删除操作，只记录警告
                    }
                }

                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "删除成功");
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "记录不存在或已删除");
            }
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("删除图书馆管理员失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    /** 获取图书馆管理员详细信息（ADMIN/OPERATION） */
    @RequestMapping(value = "/api/librarians/{libUserId}", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-获取图书馆管理员详细信息")
    public ResponseEntity<Map<String, Object>> getLibrarianDetail(@PathVariable("libUserId") Long libUserId) {
        Map<String, Object> resp = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            boolean allowed = authorities.stream().anyMatch(a ->
                    "ROLE_ADMIN".equals(a.getAuthority()) || "ROLE_OPERATION".equals(a.getAuthority())
            );
            if (!allowed) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限");
                return ResponseEntity.ok(resp);
            }
            if (libUserId == null || libUserId <= 0) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "参数不完整");
                return ResponseEntity.ok(resp);
            }

            // 查询管理员详细信息
            LibLibrarianView detail = libUserCustomMapper.selectLibrarianDetail(libUserId);
            if (detail == null) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "管理员信息不存在");
                return ResponseEntity.ok(resp);
            }

            resp.put("code", InterfaceContant.ReturnFlag.API_T);
            resp.put("message", "查询成功");
            resp.put("data", detail);
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("获取图书馆管理员详细信息失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    // ==================== 角色切换相关API ====================

    /**
     * 后台-查询图书馆管理员列表（仅系统管理员/运营可用）
     * @param request HTTP请求对象
     * @param page 页码（从1开始）
     * @param pageSize 每页大小（默认20，最大200）
     * @param keyword 关键词（按用户邮箱或图书馆名称模糊查询）
     */
    @RequestMapping(value = "/api/librarians", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-图书馆管理员列表",
            description = "查询 lib_user 表中状态为已邀请/已确认的记录，联表返回图书馆名称。支持分页与关键词筛选（邮箱/图书馆名称）。")
    @io.swagger.v3.oas.annotations.responses.ApiResponses({
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功")
    })
    public ResponseEntity<Map<String, Object>> listLibrarians(
            HttpServletRequest request,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword
    ) {
        Map<String, Object> resp = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            boolean allowed = authorities.stream().anyMatch(a ->
                    "ROLE_ADMIN".equals(a.getAuthority()) || "ROLE_OPERATION".equals(a.getAuthority())
            );
            if (!allowed) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限");
                return ResponseEntity.ok(resp);
            }

            int p = (page == null || page < 1) ? 1 : page;
            int ps = (pageSize == null || pageSize < 1) ? 20 : Math.min(pageSize, 200);
            int offset = (p - 1) * ps;

            String kw = StringUtils.isBlank(keyword) ? null : "%" + keyword.trim() + "%";
            List<LibLibrarianView> records = libUserCustomMapper.selectLibrarians(kw, offset, ps);
            int total = libUserCustomMapper.countLibrarians(kw);

            Map<String, Object> data = new HashMap<>();
            data.put("records", records);
            data.put("currentPage", p);
            data.put("pageSize", ps);
            data.put("totalRecord", total);

            resp.put("code", InterfaceContant.ReturnFlag.API_T);
            resp.put("message", "查询成功");
            resp.put("data", data);
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("查询图书馆管理员失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }
    
    /**
     * 获取当前用户可切换的图书馆列表
     * 系统管理员可以看到所有图书馆，图书馆管理员只能看到自己的图书馆
     *
     * @param request HTTP请求对象
     * @return 可切换的图书馆列表
     */
    @RequestMapping(value = "/api/switchable-libraries", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-获取可切换的图书馆/角色列表", description = "系统管理员与运营可查看所有图书馆，同时提供'系统管理员模式'；图书馆管理员仅能查看其可管理的图书馆。")
    @io.swagger.v3.oas.annotations.responses.ApiResponses({
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "获取成功",
                    content = @io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = LibrarySwitchOption.class)))
    })
    public ResponseEntity<Map<String, Object>> getSwitchableLibraries(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            String userEmail = authentication != null ? authentication.getName() : null;
            if (userEmail != null) { userEmail = userEmail.trim().toLowerCase(); }

            // 获取所有启用的图书馆（供系统管理员/运营使用）
            LibLibrary queryRecord = new LibLibrary();
            queryRecord.setStatus(1); // 只查询启用的图书馆
            Page<LibLibrary> page = new Page<>(1, 100); // 简单分页
            page = libLibraryService.queryRecords(page, queryRecord);
            List<LibLibrary> librariesAll = page != null ? page.getRecords() : new ArrayList<>();

            List<LibrarySwitchOption> options = new ArrayList<>();

            // 检查用户权限
            boolean isSystemAdmin = authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));
            boolean isLibrarian = authorities.stream().anyMatch(auth -> "ROLE_LIBRARIAN".equals(auth.getAuthority()));
            boolean isOperationAdmin = authorities.stream().anyMatch(auth -> "ROLE_OPERATION".equals(auth.getAuthority()));

            if (isSystemAdmin || isOperationAdmin) {
                // 系统管理员和运营管理员可以看到所有图书馆
                for (LibLibrary library : librariesAll) {
                    if (library.getStatus() != null && library.getStatus() == 1) {
                        LibrarySwitchOption option = new LibrarySwitchOption();
                        option.setLibraryId(library.getLibraryId());
                        option.setLibraryName(library.getName());
                        option.setLibraryKey(library.getKey());
                        option.setRoleType("LIBRARY_ADMIN");
                        option.setIcon("fa-building");
                        option.setIconColor("text-primary");
                        option.setDescription(library.getDescription());
                        options.add(option);
                    }
                }
            } else if (isLibrarian && userEmail != null) {
                // 图书馆管理员仅能看到自己管理的图书馆
                List<LibLibrary> libraries = libUserCustomMapper.selectEnabledLibrariesByUserEmail(userEmail);
                for (LibLibrary library : libraries) {
                    LibrarySwitchOption option = new LibrarySwitchOption();
                    option.setLibraryId(library.getLibraryId());
                    option.setLibraryName(library.getName());
                    option.setLibraryKey(library.getKey());
                    option.setRoleType("LIBRARY_ADMIN");
                    option.setDescription("管理 " + library.getName());
                    option.setIcon("fa-building");
                    option.setIconColor("text-info");
                    options.add(option);
                }
            }

            result.put("code", InterfaceContant.ReturnFlag.API_T);
            result.put("message", "获取成功");
            result.put("data", options);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取可切换图书馆列表失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 切换到指定图书馆角色
     *
     * @param libraryId 图书馆ID，0表示系统管理员模式
     * @param request HTTP请求对象
     * @return 切换结果
     */
    @RequestMapping(value = "/api/switch/{libraryId}", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-切换图书馆/角色", description = "libraryId=0 表示切换到系统管理员模式；否则切换到指定图书馆的管理员模式。")
    @io.swagger.v3.oas.annotations.responses.ApiResponses({
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "切换成功",
                    content = @io.swagger.v3.oas.annotations.media.Content(schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = LibrarySwitchResult.class)))
    })
    public ResponseEntity<Map<String, Object>> switchToLibrary(
            @PathVariable Long libraryId,
            HttpServletRequest request) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String userEmail = authentication != null ? authentication.getName() : null;
            if (userEmail != null) { userEmail = userEmail.trim().toLowerCase(); }

            HttpSession session = request.getSession();

            // 角色判断
            Collection<? extends GrantedAuthority> authorities = authentication != null ? authentication.getAuthorities() : new ArrayList<>();
            boolean isSystemAdmin = authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));
            boolean isOperationAdmin = authorities.stream().anyMatch(auth -> "ROLE_OPERATION".equals(auth.getAuthority()));
            boolean isLibrarian = authorities.stream().anyMatch(auth -> "ROLE_LIBRARIAN".equals(auth.getAuthority()));

            if (libraryId == 0) {
                // 切换到系统管理员模式：仅 ADMIN/OPERATION 允许
                if (!(isSystemAdmin || isOperationAdmin)) {
                    result.put("code", InterfaceContant.ReturnFlag.API_F);
                    result.put("message", "无权限切换到系统管理员模式");
                    return ResponseEntity.ok(result);
                }
                session.setAttribute("currentLibraryId", 0L);
                session.setAttribute("currentLibraryName", "系统管理员");
                session.setAttribute("currentRoleType", "SYSTEM_ADMIN");

                LibrarySwitchResult switchResult = new LibrarySwitchResult();
                switchResult.setLibraryId(0L);
                switchResult.setLibraryName("系统管理员");
                switchResult.setRoleType("SYSTEM_ADMIN");
                switchResult.setSwitchTime(System.currentTimeMillis());

                result.put("code", InterfaceContant.ReturnFlag.API_T);
                result.put("message", "切换成功");
                result.put("data", switchResult);

                log.info("用户 {} 切换到系统管理员模式", userEmail);
                return ResponseEntity.ok(result);

            } else {
                // 切换到指定图书馆
                LibLibrary library = libLibraryService.queryRecordByPrimaryKey(libraryId).getResult();
                if (library == null) {
                    result.put("code", InterfaceContant.ReturnFlag.API_F);
                    result.put("message", "指定的图书馆不存在");
                    return ResponseEntity.ok(result);
                }

                if (library.getStatus() == null || library.getStatus() != 1) {
                    result.put("code", InterfaceContant.ReturnFlag.API_F);
                    result.put("message", "指定的图书馆已禁用");
                    return ResponseEntity.ok(result);
                }

                // 权限校验：
                if (isSystemAdmin || isOperationAdmin) {
                    // 允许
                } else if (isLibrarian) {
                    // 必须具有已确认的管理员关系
                    int c = libUserMapper.countConfirmedByUserAndLibrary(userEmail, libraryId);
                    if (c <= 0) {
                        result.put("code", InterfaceContant.ReturnFlag.API_F);
                        result.put("message", "无权限管理该图书馆");
                        return ResponseEntity.ok(result);
                    }
                } else {
                    result.put("code", InterfaceContant.ReturnFlag.API_F);
                    result.put("message", "无权限");
                    return ResponseEntity.ok(result);
                }

                // 设置会话中的当前图书馆信息
                session.setAttribute("currentLibraryId", library.getLibraryId());
                session.setAttribute("currentLibraryName", library.getName());
                session.setAttribute("currentLibraryKey", library.getKey());
                session.setAttribute("currentRoleType", "LIBRARY_ADMIN");

                LibrarySwitchResult switchResult = new LibrarySwitchResult();
                switchResult.setLibraryId(library.getLibraryId());
                switchResult.setLibraryName(library.getName());
                switchResult.setLibraryKey(library.getKey());
                switchResult.setRoleType("LIBRARY_ADMIN");
                switchResult.setSwitchTime(System.currentTimeMillis());

                result.put("code", InterfaceContant.ReturnFlag.API_T);
                result.put("message", "切换成功");
                result.put("data", switchResult);

                log.info("用户 {} 切换到图书馆: {} (ID: {})", userEmail, library.getName(), libraryId);
                return ResponseEntity.ok(result);
            }
            
        } catch (Exception e) {
            log.error("切换图书馆角色失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "切换失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取当前角色状态
     *
     * @param request HTTP请求对象
     * @return 当前角色状态
     */
    @RequestMapping(value = "/api/current-status", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-获取当前角色状态", description = "返回当前会话中所处的角色/图书馆信息。若未设置则默认视为系统管理员模式。")
    public ResponseEntity<Map<String, Object>> getCurrentStatus(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            HttpSession session = request.getSession();
            // 如果当前会话未设置角色，且为图书馆管理员，则默认选中其拥有的第一个图书馆
            if (session.getAttribute("currentRoleType") == null) {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                String userEmail = authentication != null ? authentication.getName() : null;
                if (userEmail != null) { userEmail = userEmail.trim().toLowerCase(); }
                Collection<? extends GrantedAuthority> authorities = authentication != null ? authentication.getAuthorities() : new ArrayList<>();
                boolean isLibrarian = authorities.stream().anyMatch(auth -> "ROLE_LIBRARIAN".equals(auth.getAuthority()));
                if (isLibrarian && userEmail != null) {
                    List<LibLibrary> libraries = libUserCustomMapper.selectEnabledLibrariesByUserEmail(userEmail);
                    if (libraries != null && !libraries.isEmpty()) {
                        LibLibrary first = libraries.get(0);
                        session.setAttribute("currentLibraryId", first.getLibraryId());
                        session.setAttribute("currentLibraryName", first.getName());
                        session.setAttribute("currentLibraryKey", first.getKey());
                        session.setAttribute("currentRoleType", "LIBRARY_ADMIN");
                    }
                }
            }

            LibrarySwitchResult switchResult = new LibrarySwitchResult();
            switchResult.setLibraryId((Long) session.getAttribute("currentLibraryId"));
            switchResult.setLibraryName((String) session.getAttribute("currentLibraryName"));
            switchResult.setLibraryKey((String) session.getAttribute("currentLibraryKey"));
            switchResult.setRoleType((String) session.getAttribute("currentRoleType"));
            switchResult.setSwitchTime(System.currentTimeMillis());
            
            // 如果没有设置，使用默认值
            if (switchResult.getRoleType() == null) {
                switchResult.setRoleType("SYSTEM_ADMIN");
                switchResult.setLibraryName("系统管理员");
            }
            
            result.put("code", InterfaceContant.ReturnFlag.API_T);
            result.put("message", "获取成功");
            result.put("data", switchResult);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取当前角色状态失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    // ==================== 图书馆（lib_library）后台 API ====================
    
    /** 后台-图书馆分页列表 */
    @RequestMapping(value = "/api/libraries", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-图书馆分页列表", description = "支持 name/key/status 过滤。系统管理员和运营管理员可查看所有图书馆，图书馆管理员只能查看自己管理的图书馆。")
    public ResponseEntity<Map<String, Object>> listLibraries(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize,
            HttpServletRequest request
    ) {
        Map<String, Object> resp = new HashMap<>();
        try {
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            String userEmail = authentication != null ? authentication.getName() : null;
            if (userEmail != null) { userEmail = userEmail.trim().toLowerCase(); }

            boolean isSystemAdmin = authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));
            boolean isOperationAdmin = authorities.stream().anyMatch(auth -> "ROLE_OPERATION".equals(auth.getAuthority()));
            boolean isLibrarian = authorities.stream().anyMatch(auth -> "ROLE_LIBRARIAN".equals(auth.getAuthority()));

            List<LibLibrary> records = new ArrayList<>();
            long totalRecord = 0;

            if (isSystemAdmin || isOperationAdmin) {
                // 系统管理员和运营管理员：查看所有图书馆
                LibLibrary cond = new LibLibrary();
                cond.setIsDeleted(0);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) cond.setName(name.trim());
                if (status != null) cond.setStatus(status);

                Page<LibLibrary> page = new Page<>(pageNo, pageSize);
                page = libLibraryService.queryRecords(page, cond);
                records = page != null ? page.getRecords() : new ArrayList<>();
                totalRecord = page != null ? page.getTotalRecord() : 0;

                // key过滤
                if (org.apache.commons.lang3.StringUtils.isNotBlank(key)) {
                    String k = key.trim();
                    List<LibLibrary> filtered = new ArrayList<>();
                    for (LibLibrary r : records) {
                        if (r != null && k.equals(r.getKey())) filtered.add(r);
                    }
                    records = filtered;
                    totalRecord = filtered.size(); // 重新计算总数
                }

            } else if (isLibrarian && userEmail != null) {
                // 图书馆管理员：只能查看自己管理的图书馆
                List<LibLibrary> managedLibraries = libUserCustomMapper.selectEnabledLibrariesByUserEmail(userEmail);

                // 应用过滤条件
                List<LibLibrary> filtered = new ArrayList<>();
                for (LibLibrary library : managedLibraries) {
                    boolean match = true;

                    // name过滤
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(name) &&
                        (library.getName() == null || !library.getName().contains(name.trim()))) {
                        match = false;
                    }

                    // key过滤
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(key) &&
                        !key.trim().equals(library.getKey())) {
                        match = false;
                    }

                    // status过滤
                    if (status != null && !status.equals(library.getStatus())) {
                        match = false;
                    }

                    if (match) {
                        filtered.add(library);
                    }
                }

                // 手动分页
                totalRecord = filtered.size();
                int startIndex = (pageNo - 1) * pageSize;
                int endIndex = Math.min(startIndex + pageSize, filtered.size());
                if (startIndex < filtered.size()) {
                    records = filtered.subList(startIndex, endIndex);
                }

            } else {
                // 无权限
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限访问图书馆列表");
                return ResponseEntity.ok(resp);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("records", records);
            data.put("currentPage", pageNo);
            data.put("pageSize", pageSize);
            data.put("totalRecord", totalRecord);

            resp.put("code", InterfaceContant.ReturnFlag.API_T);
            resp.put("message", "查询成功");
            resp.put("data", data);
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("查询图书馆列表失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    /** 后台-图书馆详情 */
    @RequestMapping(value = "/api/libraries/{id}", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-图书馆详情", description = "系统管理员和运营管理员可查看所有图书馆详情，图书馆管理员只能查看自己管理的图书馆详情。")
    public ResponseEntity<Map<String, Object>> getLibrary(@PathVariable("id") Long id, HttpServletRequest request) {
        Map<String, Object> resp = new HashMap<>();
        try {
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            String userEmail = authentication != null ? authentication.getName() : null;
            if (userEmail != null) { userEmail = userEmail.trim().toLowerCase(); }

            boolean isSystemAdmin = authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));
            boolean isOperationAdmin = authorities.stream().anyMatch(auth -> "ROLE_OPERATION".equals(auth.getAuthority()));
            boolean isLibrarian = authorities.stream().anyMatch(auth -> "ROLE_LIBRARIAN".equals(auth.getAuthority()));

            if (isSystemAdmin || isOperationAdmin) {
                // 系统管理员和运营管理员：可以查看任何图书馆详情
            } else if (isLibrarian && userEmail != null) {
                // 图书馆管理员：只能查看自己管理的图书馆
                int count = libUserMapper.countConfirmedByUserAndLibrary(userEmail, id);
                if (count <= 0) {
                    resp.put("code", InterfaceContant.ReturnFlag.API_F);
                    resp.put("message", "无权限查看该图书馆详情");
                    return ResponseEntity.ok(resp);
                }
            } else {
                // 无权限
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限访问图书馆详情");
                return ResponseEntity.ok(resp);
            }

            ExecuteResult<LibLibrary> result = libLibraryService.queryRecordByPrimaryKey(id);
            if (result != null && result.isSuccess() && result.getResult() != null) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "查询成功");
                resp.put("data", result.getResult());
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "记录不存在");
            }
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("查询图书馆详情失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    /** 后台-key唯一性校验 */
    @RequestMapping(value = "/api/libraries/check-key", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-检查key唯一性")
    public ResponseEntity<Map<String, Object>> checkKey(
            @RequestParam("key") String key,
            @RequestParam(value = "excludeId", required = false) Long excludeId
    ) {
        Map<String, Object> resp = new HashMap<>();
        try {
            boolean exists = libLibraryService.isKeyExists(key, excludeId);
            Map<String, Object> data = new HashMap<>();
            data.put("exists", exists);
            resp.put("code", InterfaceContant.ReturnFlag.API_T);
            resp.put("message", "OK");
            resp.put("data", data);
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("检查key失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    private boolean hasAdminOrOperation(Collection<? extends GrantedAuthority> authorities) {
        return authorities.stream().anyMatch(a ->
                "ROLE_ADMIN".equals(a.getAuthority()) || "ROLE_OPERATION".equals(a.getAuthority())
        );
    }

    private boolean isLibrarian(Collection<? extends GrantedAuthority> authorities) {
        return authorities.stream().anyMatch(a -> "ROLE_LIBRARIAN".equals(a.getAuthority()));
    }

    private static boolean isValidKey(String key) {
        if (org.apache.commons.lang3.StringUtils.isBlank(key)) return false;
        return key.matches("^[a-z0-9_-]{3,50}$");
    }

    /** 后台-创建图书馆（管理员/运营） */
    @RequestMapping(value = "/api/libraries", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-新建图书馆", description = "只有系统管理员和运营管理员可以创建图书馆")
    public ResponseEntity<Map<String, Object>> createLibrary(
            @RequestParam("name") String name,
            @RequestParam("key") String key,
            @RequestParam(value = "status", required = false, defaultValue = "1") Integer status,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "max_borrow_books", required = false) Integer maxBorrowBooks,
            @RequestParam(value = "default_loan_days", required = false) Integer defaultLoanDays,
            @RequestParam(value = "remark", required = false) String remark,
            HttpServletRequest request
    ) {
        Map<String, Object> resp = new HashMap<>();
        try {
            // 权限验证：只有系统管理员和运营管理员可以创建图书馆
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            if (!hasAdminOrOperation(authorities)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限创建图书馆");
                return ResponseEntity.ok(resp);
            }
            boolean isSystemAdmin = authorities.stream().anyMatch(a -> "ROLE_ADMIN".equals(a.getAuthority()));

            if (org.apache.commons.lang3.StringUtils.isBlank(name)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "名称不能为空");
                return ResponseEntity.ok(resp);
            }
            if (!isValidKey(key)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "Key不合法");
                return ResponseEntity.ok(resp);
            }
            if (libLibraryService.isKeyExists(key, null)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "Key已存在");
                return ResponseEntity.ok(resp);
            }
            if (maxBorrowBooks != null && (maxBorrowBooks < 1 || maxBorrowBooks > 100)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "借阅上限范围1-100");
                return ResponseEntity.ok(resp);
            }
            if (defaultLoanDays != null && (defaultLoanDays < 1 || defaultLoanDays > 90)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "默认借期范围1-90");
                return ResponseEntity.ok(resp);
            }

            LibLibrary rec = new LibLibrary();
            rec.setName(name.trim());
            rec.setKey(key.trim());
            // 仅系统管理员可设置状态，其他创建者（运营）强制启用
            rec.setStatus(isSystemAdmin ? (status == null ? 1 : status) : 1);
            rec.setDescription(description);
            // 仅系统管理员可设置备注
            rec.setRemark(isSystemAdmin ? remark : null);
            rec.setMaxBorrowBooks(maxBorrowBooks);
            rec.setDefaultLoanDays(defaultLoanDays);
            rec.setIsDeleted(0);
            ExecuteResult<LibLibrary> result = libLibraryService.addRecord(rec);
            if (result != null && result.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "创建成功");
                resp.put("data", result.getResult());
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "创建失败");
            }
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("创建图书馆失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "创建失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    /** 后台-更新图书馆（POST） */
    @RequestMapping(value = "/api/libraries/{id}", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-更新图书馆", description = "系统管理员和运营管理员可以更新任何图书馆，图书馆管理员只能更新自己管理的图书馆")
    public ResponseEntity<Map<String, Object>> updateLibrary(
            @PathVariable("id") Long id,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "max_borrow_books", required = false) Integer maxBorrowBooks,
            @RequestParam(value = "default_loan_days", required = false) Integer defaultLoanDays,
            @RequestParam(value = "remark", required = false) String remark,
            HttpServletRequest request
    ) {
        Map<String, Object> resp = new HashMap<>();
        try {
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            String userEmail = authentication != null ? authentication.getName() : null;
            if (userEmail != null) { userEmail = userEmail.trim().toLowerCase(); }

            boolean isSystemAdmin = authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));
            boolean isOperationAdmin = authorities.stream().anyMatch(auth -> "ROLE_OPERATION".equals(auth.getAuthority()));
            boolean isLibrarian = authorities.stream().anyMatch(auth -> "ROLE_LIBRARIAN".equals(auth.getAuthority()));

            if (isSystemAdmin || isOperationAdmin) {
                // 系统管理员和运营管理员：可以更新任何图书馆
            } else if (isLibrarian && userEmail != null) {
                // 图书馆管理员：只能更新自己管理的图书馆
                int count = libUserMapper.countConfirmedByUserAndLibrary(userEmail, id);
                if (count <= 0) {
                    resp.put("code", InterfaceContant.ReturnFlag.API_F);
                    resp.put("message", "无权限更新该图书馆");
                    return ResponseEntity.ok(resp);
                }

                // 图书馆管理员不能修改状态（启用/禁用）
                if (status != null) {
                    resp.put("code", InterfaceContant.ReturnFlag.API_F);
                    resp.put("message", "图书馆管理员无权限修改图书馆状态");
                    return ResponseEntity.ok(resp);
                }
                // 图书馆管理员不能修改备注
                if (remark != null) {
                    resp.put("code", InterfaceContant.ReturnFlag.API_F);
                    resp.put("message", "图书馆管理员无权限修改备注");
                    return ResponseEntity.ok(resp);
                }
            } else {
                // 无权限
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限更新图书馆");
                return ResponseEntity.ok(resp);
            }

            LibLibrary toUpdate = new LibLibrary();
            toUpdate.setLibraryId(id);

            if (isSystemAdmin || isOperationAdmin) {
                if (name != null) {
                    if (org.apache.commons.lang3.StringUtils.isBlank(name)) {
                        resp.put("code", InterfaceContant.ReturnFlag.API_F);
                        resp.put("message", "名称不能为空");
                        return ResponseEntity.ok(resp);
                    }
                    toUpdate.setName(name.trim());
                }
                if (key != null) {
                    if (!isValidKey(key)) {
                        resp.put("code", InterfaceContant.ReturnFlag.API_F);
                        resp.put("message", "Key不合法");
                        return ResponseEntity.ok(resp);
                    }
                    if (libLibraryService.isKeyExists(key, id)) {
                        resp.put("code", InterfaceContant.ReturnFlag.API_F);
                        resp.put("message", "Key已存在");
                        return ResponseEntity.ok(resp);
                    }
                    toUpdate.setKey(key.trim());
                }
                // 仅系统管理员可修改状态与备注
                if (isSystemAdmin && status != null) toUpdate.setStatus(status);
                if (description != null) toUpdate.setDescription(description);
                if (isSystemAdmin && remark != null) toUpdate.setRemark(remark);
                if (maxBorrowBooks != null) {
                    if (maxBorrowBooks < 1 || maxBorrowBooks > 100) {
                        resp.put("code", InterfaceContant.ReturnFlag.API_F);
                        resp.put("message", "借阅上限范围1-100");
                        return ResponseEntity.ok(resp);
                    }
                    toUpdate.setMaxBorrowBooks(maxBorrowBooks);
                }
                if (defaultLoanDays != null) {
                    if (defaultLoanDays < 1 || defaultLoanDays > 90) {
                        resp.put("code", InterfaceContant.ReturnFlag.API_F);
                        resp.put("message", "默认借期范围1-90");
                        return ResponseEntity.ok(resp);
                    }
                    toUpdate.setDefaultLoanDays(defaultLoanDays);
                }
            } else if (isLibrarian && userEmail != null) {
                HttpSession session = request.getSession();
                Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");
                if (currentLibraryId == null || !id.equals(currentLibraryId)) {
                    resp.put("code", InterfaceContant.ReturnFlag.API_F);
                    resp.put("message", "无权限：仅能修改当前图书馆");
                    return ResponseEntity.ok(resp);
                }
                int c = libUserMapper.countConfirmedByUserAndLibrary(userEmail, currentLibraryId);
                if (c <= 0) {
                    resp.put("code", InterfaceContant.ReturnFlag.API_F);
                    resp.put("message", "无权限：需馆员关联");
                    return ResponseEntity.ok(resp);
                }
                if (maxBorrowBooks != null) {
                    if (maxBorrowBooks < 1 || maxBorrowBooks > 100) {
                        resp.put("code", InterfaceContant.ReturnFlag.API_F);
                        resp.put("message", "借阅上限范围1-100");
                        return ResponseEntity.ok(resp);
                    }
                    toUpdate.setMaxBorrowBooks(maxBorrowBooks);
                }
                if (defaultLoanDays != null) {
                    if (defaultLoanDays < 1 || defaultLoanDays > 90) {
                        resp.put("code", InterfaceContant.ReturnFlag.API_F);
                        resp.put("message", "默认借期范围1-90");
                        return ResponseEntity.ok(resp);
                    }
                    toUpdate.setDefaultLoanDays(defaultLoanDays);
                }
                // 馆员可修改描述
                if (description != null) toUpdate.setDescription(description);
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限");
                return ResponseEntity.ok(resp);
            }

            ExecuteResult<LibLibrary> result = libLibraryService.updateRecord(toUpdate);
            if (result != null && result.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "更新成功");
                resp.put("data", result.getResult());
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "更新失败");
            }
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("更新图书馆失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    /** 后台-启用/禁用 */
    @RequestMapping(value = "/api/libraries/{id}/toggle", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-启用/禁用图书馆")
    public ResponseEntity<Map<String, Object>> toggleLibrary(@PathVariable("id") Long id) {
        Map<String, Object> resp = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            if (!hasAdminOrOperation(authorities)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限");
                return ResponseEntity.ok(resp);
            }
            ExecuteResult<LibLibrary> existed = libLibraryService.queryRecordByPrimaryKey(id);
            if (existed == null || !existed.isSuccess() || existed.getResult() == null) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "记录不存在");
                return ResponseEntity.ok(resp);
            }
            LibLibrary lib = existed.getResult();
            Integer st = (lib.getStatus() == null || lib.getStatus() == 0) ? 1 : 0;
            LibLibrary toUpdate = new LibLibrary();
            toUpdate.setLibraryId(id);
            toUpdate.setStatus(st);
            ExecuteResult<LibLibrary> result = libLibraryService.updateRecord(toUpdate);
            if (result != null && result.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "切换成功");
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "切换失败");
            }
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("切换状态失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "切换失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    /** 后台-软删除 */
    @RequestMapping(value = "/api/libraries/{id}/delete", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-软删除图书馆")
    public ResponseEntity<Map<String, Object>> deleteLibrary(@PathVariable("id") Long id) {
        Map<String, Object> resp = new HashMap<>();
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            if (!hasAdminOrOperation(authorities)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限");
                return ResponseEntity.ok(resp);
            }

            // 依赖检查
            com.aaron.spring.model.LibBookExample bookEx = new com.aaron.spring.model.LibBookExample();
            com.aaron.spring.model.LibBookExample.Criteria bc = bookEx.createCriteria();
            bc.andLibraryIdEqualTo(id);
            bc.andStatusEqualTo(1);
            bc.andIsDeletedEqualTo(0);
            long activeBooks = libBookMapper.countByExample(bookEx);

            long activeAdmins = libUserMapper.countActiveAdminsByLibrary(id);

            com.aaron.spring.model.LibBorrowExample borrowEx = new com.aaron.spring.model.LibBorrowExample();
            com.aaron.spring.model.LibBorrowExample.Criteria brc = borrowEx.createCriteria();
            brc.andLibraryIdEqualTo(id);
            brc.andStatusEqualTo(0);
            long borrowing = libBorrowMapper.countByExample(borrowEx);

            java.util.List<String> blocks = new java.util.ArrayList<>();
            if (activeBooks > 0) blocks.add("存在上架馆藏");
            if (activeAdmins > 0) blocks.add("存在活跃馆员");
            if (borrowing > 0) blocks.add("存在借阅中记录");
            if (!blocks.isEmpty()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", String.join("；", blocks));
                return ResponseEntity.ok(resp);
            }

            ExecuteResult<String> del = libLibraryService.deleteRecordByPrimaryKey(id);
            if (del != null && del.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "删除成功");
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "删除失败");
            }
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("删除图书馆失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.ok(resp);
        }
    }

    // ==================== 书单管理 API ====================

    /**
     * 获取图书馆书单列表（后台管理）
     */
    @RequestMapping(value = "/api/book-lists", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-获取图书馆书单列表")
    public ResponseEntity<Map<String, Object>> getBookLists(
            @RequestParam(value = "libraryId", required = false) Long libraryId,
            HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前图书馆ID
            Long currentLibraryId = libraryId;
            if (currentLibraryId == null) {
                HttpSession session = request.getSession();
                currentLibraryId = (Long) session.getAttribute("currentLibraryId");
            }
            
            if (currentLibraryId == null || currentLibraryId == 0) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "请先选择图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 获取书单列表
            List<LibBookList> bookLists = libBookListService.getOrderedListsByLibraryId(currentLibraryId);
            
            result.put("code", InterfaceContant.ReturnFlag.API_T);
            result.put("message", "查询成功");
            result.put("data", bookLists);
            
        } catch (Exception e) {
            log.error("获取书单列表失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 创建书单
     */
    @RequestMapping(value = "/api/book-lists", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-创建书单")
    public ResponseEntity<Map<String, Object>> createBookList(
            @RequestParam("listName") String listName,
            @RequestParam(value = "listNameTc", required = false) String listNameTc,
            @RequestParam(value = "listNameEn", required = false) String listNameEn,
            @RequestParam(value = "listAbstract", required = false) String listAbstract,
            @RequestParam(value = "bannerUrl", required = false) String bannerUrl,
            @RequestParam(value = "bookIdText", required = false) String bookIdText,
            @RequestParam(value = "isShow", required = false, defaultValue = "1") Integer isShow,
            @RequestParam(value = "showOrder", required = false) Integer showOrder,
            HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");
            
            if (currentLibraryId == null || currentLibraryId == 0) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "请先选择图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 构建书单对象
            LibBookList bookList = new LibBookList();
            bookList.setLibraryId(currentLibraryId);
            bookList.setListName(listName);
            bookList.setListNameTc(listNameTc);
            bookList.setListNameEn(listNameEn);
            bookList.setListAbstract(listAbstract);
            bookList.setBannerUrl(bannerUrl);
            bookList.setBookIdText(bookIdText);
            bookList.setIsShow(isShow);

            // 设置排序号，如果没有提供则使用服务层的默认逻辑
            if (showOrder != null) {
                bookList.setShowOrder(showOrder);
            }
            
            // 创建书单
            ExecuteResult<LibBookList> createResult = libBookListService.createBookList(bookList);
            
            if (createResult.isSuccess()) {
                result.put("code", InterfaceContant.ReturnFlag.API_T);
                result.put("message", "创建成功");
                result.put("data", createResult.getResult());
            } else {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", createResult.getErrorMessages());
            }
            
        } catch (Exception e) {
            log.error("创建书单失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "创建失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 更新书单
     */
    @RequestMapping(value = "/api/book-lists/{listId}", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-更新书单")
    public ResponseEntity<Map<String, Object>> updateBookList(
            @PathVariable("listId") Long listId,
            @RequestParam(value = "listName", required = false) String listName,
            @RequestParam(value = "listNameTc", required = false) String listNameTc,
            @RequestParam(value = "listNameEn", required = false) String listNameEn,
            @RequestParam(value = "listAbstract", required = false) String listAbstract,
            @RequestParam(value = "bannerUrl", required = false) String bannerUrl,
            @RequestParam(value = "bookIdText", required = false) String bookIdText,
            @RequestParam(value = "isShow", required = false) Integer isShow,
            @RequestParam(value = "showOrder", required = false) Integer showOrder,
            HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");
            
            if (currentLibraryId == null || currentLibraryId == 0) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "请先选择图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 构建更新对象
            LibBookList bookList = new LibBookList();
            bookList.setLibListId(listId);
            if (listName != null) bookList.setListName(listName);
            if (listNameTc != null) bookList.setListNameTc(listNameTc);
            if (listNameEn != null) bookList.setListNameEn(listNameEn);
            if (listAbstract != null) bookList.setListAbstract(listAbstract);
            if (bannerUrl != null) bookList.setBannerUrl(bannerUrl);
            if (bookIdText != null) bookList.setBookIdText(bookIdText);
            if (isShow != null) bookList.setIsShow(isShow);
            if (showOrder != null) bookList.setShowOrder(showOrder);
            
            // 更新书单
            ExecuteResult<LibBookList> updateResult = libBookListService.updateBookList(bookList);
            
            if (updateResult.isSuccess()) {
                result.put("code", InterfaceContant.ReturnFlag.API_T);
                result.put("message", "更新成功");
                result.put("data", updateResult.getResult());
            } else {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", updateResult.getErrorMessages());
            }
            
        } catch (Exception e) {
            log.error("更新书单失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "更新失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 删除书单
     */
    @RequestMapping(value = "/api/book-lists/{listId}/delete", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-删除书单")
    public ResponseEntity<Map<String, Object>> deleteBookList(
            @PathVariable("listId") Long listId,
            HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");
            
            if (currentLibraryId == null || currentLibraryId == 0) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "请先选择图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 删除书单
            ExecuteResult<String> deleteResult = libBookListService.deleteBookList(listId, currentLibraryId);
            
            if (deleteResult.isSuccess()) {
                result.put("code", InterfaceContant.ReturnFlag.API_T);
                result.put("message", "删除成功");
            } else {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", deleteResult.getErrorMessages());
            }
            
        } catch (Exception e) {
            log.error("删除书单失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "删除失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 切换书单显示状态
     */
    @RequestMapping(value = "/api/book-lists/{listId}/toggle", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-切换书单显示状态")
    public ResponseEntity<Map<String, Object>> toggleBookListShow(
            @PathVariable("listId") Long listId,
            HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");
            
            if (currentLibraryId == null || currentLibraryId == 0) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "请先选择图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 切换显示状态
            ExecuteResult<String> toggleResult = libBookListService.toggleBookListShow(listId, currentLibraryId);
            
            if (toggleResult.isSuccess()) {
                result.put("code", InterfaceContant.ReturnFlag.API_T);
                result.put("message", toggleResult.getResult());
            } else {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", toggleResult.getErrorMessages());
            }
            
        } catch (Exception e) {
            log.error("切换书单显示状态失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "操作失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 拖动排序 - 移动书单位置
     */
    @RequestMapping(value = "/api/book-lists/{listId}/move", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-移动书单位置")
    public ResponseEntity<Map<String, Object>> moveBookList(
            @PathVariable("listId") Long listId,
            @RequestParam("targetListId") Long targetListId,
            @RequestParam("position") String position,
            HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");
            
            if (currentLibraryId == null || currentLibraryId == 0) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "请先选择图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 移动书单
            ExecuteResult<String> moveResult = libBookListService.moveBookList(listId, targetListId, position, currentLibraryId);
            
            if (moveResult.isSuccess()) {
                result.put("code", InterfaceContant.ReturnFlag.API_T);
                result.put("message", "移动成功");
            } else {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", moveResult.getErrorMessages());
            }
            
        } catch (Exception e) {
            log.error("移动书单失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "移动失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 批量更新书单排序
     */
    @RequestMapping(value = "/api/book-lists/batch-order", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-批量更新书单排序")
    public ResponseEntity<Map<String, Object>> batchUpdateBookListOrder(
            @RequestBody List<Long> listIds,
            HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");
            
            if (currentLibraryId == null || currentLibraryId == 0) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "请先选择图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }
            
            // 批量更新排序
            ExecuteResult<String> updateResult = libBookListService.batchUpdateShowOrder(listIds, currentLibraryId);
            
            if (updateResult.isSuccess()) {
                result.put("code", InterfaceContant.ReturnFlag.API_T);
                result.put("message", "更新成功");
            } else {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", updateResult.getErrorMessages());
            }
            
        } catch (Exception e) {
            log.error("批量更新书单排序失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "更新失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /** 后台-设置当前图书馆 */
    @RequestMapping(value = "/api/set-current-library", method = RequestMethod.POST)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-设置当前图书馆")
    public ResponseEntity<Map<String, Object>> setCurrentLibrary(
            @RequestParam(value = "libraryId", required = true) Long libraryId,
            HttpServletRequest request
    ) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, libraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }

            // 设置到session
            HttpSession session = request.getSession();
            session.setAttribute("currentLibraryId", libraryId);

            result.put("code", InterfaceContant.ReturnFlag.API_T);
            result.put("message", "设置成功");

        } catch (Exception e) {
            log.error("设置当前图书馆失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "设置失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /** 后台-搜索书籍 */
    @RequestMapping(value = "/api/books/search", method = RequestMethod.GET)
    @ResponseBody
    @io.swagger.v3.oas.annotations.Operation(summary = "后台-搜索书籍", description = "根据关键词搜索书籍，支持书名、作者、ISBN搜索")
    public ResponseEntity<Map<String, Object>> searchBooks(
            @RequestParam(value = "libraryId", required = true) Long libraryId,
            @RequestParam(value = "keyword", required = true) String keyword,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "50") Integer size,
            HttpServletRequest request
    ) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 参数验证
            if (libraryId == null || libraryId <= 0) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "图书馆ID不能为空");
                return ResponseEntity.ok(result);
            }

            if (org.apache.commons.lang3.StringUtils.isBlank(keyword)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "搜索关键词不能为空");
                return ResponseEntity.ok(result);
            }

            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, libraryId)) {
                result.put("code", InterfaceContant.ReturnFlag.API_F);
                result.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(result);
            }

            // 参数处理
            int pageNum = Math.max(1, page);
            int pageSize = Math.min(Math.max(1, size), 100); // 限制最大100条
            String searchKeyword = keyword.trim();

            // 调用书籍服务搜索
            // 这里使用EnyanBookService进行搜索，因为图书馆的书籍实际上是引用主书库的书籍
            EnyanBook searchCondition = new EnyanBook();
            searchCondition.setSearchText(searchKeyword); // 使用searchText字段进行搜索

            // 设置分页
            Page<EnyanBook> pageObj = new Page<>();
            pageObj.setCurrentPage(pageNum);
            pageObj.setPageSize(pageSize);
            searchCondition.setPage(pageObj);

            // 执行搜索 - 使用现有的搜索方法
            Page<EnyanBook> searchResult = enyanBookService.searchBookByTitleOrAuthor(searchCondition);

            // 转换为前端需要的格式
            List<Map<String, Object>> books = new ArrayList<>();
            if (searchResult != null && searchResult.getRecords() != null) {
                for (EnyanBook book : searchResult.getRecords()) {
                    Map<String, Object> bookInfo = new HashMap<>();
                    bookInfo.put("bookId", book.getBookId());
                    bookInfo.put("title", book.getBookTitle());
                    bookInfo.put("author", book.getAuthor());
                    bookInfo.put("isbn", book.getBookIsbn());
                    bookInfo.put("publisher", book.getPublisherName());
                    bookInfo.put("publishDate", book.getPublishedAt());
                    bookInfo.put("category", book.getCategoryName());
                    bookInfo.put("language", book.getIsInCn() != null ? (book.getIsInCn() == 1 ? "简体中文" : "繁体中文") : "未知");
                    bookInfo.put("coverUrl", book.getBookCover());
                    books.add(bookInfo);
                }
            }

            // 构建返回结果
            Map<String, Object> data = new HashMap<>();
            data.put("books", books);
            data.put("currentPage", pageNum);
            data.put("pageSize", pageSize);
            data.put("totalRecord", searchResult != null ? searchResult.getTotalRecord() : 0);
            data.put("keyword", searchKeyword);

            result.put("code", InterfaceContant.ReturnFlag.API_T);
            result.put("message", "搜索成功");
            result.put("data", data);

        } catch (Exception e) {
            log.error("搜索书籍失败", e);
            result.put("code", InterfaceContant.ReturnFlag.API_F);
            result.put("message", "搜索失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 检查权限辅助方法
     */
    private boolean hasLibraryPermission(Authentication authentication, Long libraryId) {
        if (authentication == null) return false;

        Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();

        // 详细日志调试
        log.info("权限检查详情 - 用户: {}, 图书馆ID: {}", authentication.getName(), libraryId);
        for (GrantedAuthority auth : authorities) {
            log.info("用户角色: '{}' (类型: {})", auth.getAuthority(), auth.getClass().getSimpleName());
        }

        boolean isSystemAdmin = authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));
        boolean isOperationAdmin = authorities.stream().anyMatch(auth -> "ROLE_OPERATION".equals(auth.getAuthority()));

        log.info("权限检查结果 - isSystemAdmin: {}, isOperationAdmin: {}", isSystemAdmin, isOperationAdmin);

        if (isSystemAdmin || isOperationAdmin) {
            log.info("系统管理员权限通过");
            return true; // 系统管理员有所有权限
        }
        
        boolean isLibrarian = authorities.stream().anyMatch(auth -> "ROLE_LIBRARIAN".equals(auth.getAuthority()));
        if (isLibrarian) {
            String userEmail = authentication.getName();
            if (userEmail != null) {
                userEmail = userEmail.trim().toLowerCase();
                try {
                    int count = libUserMapper.countConfirmedByUserAndLibrary(userEmail, libraryId);
                    return count > 0;
                } catch (Exception e) {
                    log.error("检查图书馆权限失败", e);
                    return false;
                }
            }
        }
        
        return false;
    }

    // ==================== 馆藏图书管理 ====================

    /**
     * 馆藏图书列表页面
     */
    @RequestMapping(value = "/books", method = RequestMethod.GET)
    public String booksPage(HttpServletRequest request, ModelMap modelMap) {
        log.debug("访问馆藏图书列表页面");
        return "admin/library/books";
    }

    /**
     * 馆藏图书列表API（分页查询）
     */
    @RequestMapping(value = "/api/books", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getBooks(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "author", required = false) String author,
            @RequestParam(value = "isbn", required = false) String isbn,
            @RequestParam(value = "status", required = false) Integer status,
            HttpServletRequest request) {

        Map<String, Object> resp = new HashMap<>();

        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");

            log.debug("查询馆藏图书列表 - 当前图书馆ID: {}", currentLibraryId);

            if (currentLibraryId == null || currentLibraryId == 0) {
                // 尝试获取用户的第一个可管理图书馆作为默认值
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
                boolean isSystemAdmin = authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));
                boolean isOperationAdmin = authorities.stream().anyMatch(auth -> "ROLE_OPERATION".equals(auth.getAuthority()));

                if (isSystemAdmin || isOperationAdmin) {
                    // 系统管理员，获取第一个启用的图书馆
                    try {
                        LibLibrary queryRecord = new LibLibrary();
                        queryRecord.setStatus(1); // 只查询启用的图书馆
                        Page<LibLibrary> libraryPage = new Page<>(1, 1);
                        Page<LibLibrary> libraries = libLibraryService.queryRecords(libraryPage, queryRecord);
                        if (libraries.getRecords() != null && !libraries.getRecords().isEmpty()) {
                            currentLibraryId = libraries.getRecords().get(0).getLibraryId();
                            session.setAttribute("currentLibraryId", currentLibraryId);
                            log.info("自动设置默认图书馆ID: {}", currentLibraryId);
                        }
                    } catch (Exception e) {
                        log.warn("获取默认图书馆失败", e);
                    }
                }

                if (currentLibraryId == null || currentLibraryId == 0) {
                    resp.put("code", InterfaceContant.ReturnFlag.API_F);
                    resp.put("message", "请先选择图书馆");
                    return ResponseEntity.ok(resp);
                }
            }

            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(resp);
            }

            // 构建分页对象
            Page<LibBookWithDetail> pageObj = new Page<>();
            pageObj.setCurrentPage(page);
            pageObj.setPageSize(size);

            // 查询馆藏列表
            Page<LibBookWithDetail> result = libBookService.queryLibBooksWithConditions(
                    pageObj, currentLibraryId, title, author, isbn, status);

            resp.put("code", InterfaceContant.ReturnFlag.API_T);
            resp.put("message", "查询成功");
            resp.put("data", result.getRecords());
            resp.put("totalRecord", result.getTotalRecord());
            resp.put("currentPage", result.getCurrentPage());
            resp.put("pageSize", result.getPageSize());
            resp.put("totalPage", result.getTotalPage());

        } catch (Exception e) {
            log.error("查询馆藏图书列表失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "查询失败：" + e.getMessage());
        }

        return ResponseEntity.ok(resp);
    }

    /**
     * 获取图书详情（用于编辑）
     */
    @RequestMapping(value = "/api/books/{libBookId}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getBookDetail(
            @PathVariable Long libBookId,
            HttpServletRequest request) {

        Map<String, Object> resp = new HashMap<>();

        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");

            if (currentLibraryId == null || currentLibraryId == 0) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "请先选择图书馆");
                return ResponseEntity.ok(resp);
            }

            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(resp);
            }

            // 查询图书详情
            ExecuteResult<LibBookWithDetail> result = libBookService.getLibBookDetailForEdit(libBookId, currentLibraryId);

            if (result.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "查询成功");
                resp.put("data", result.getResult());
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", result.getErrorMessages().get(0));
            }

        } catch (Exception e) {
            log.error("查询图书详情失败, libBookId: {}", libBookId, e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "查询失败：" + e.getMessage());
        }

        return ResponseEntity.ok(resp);
    }

    /**
     * 更新图书信息
     */
    @RequestMapping(value = "/api/books/{libBookId}", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> updateBook(
            @PathVariable Long libBookId,
            @RequestParam(value = "totalCopies", required = false) Integer totalCopies,
            @RequestParam(value = "availableCopies", required = false) Integer availableCopies,
            @RequestParam(value = "loanDays", required = false) Integer loanDays,
            @RequestParam(value = "status", required = false) Integer status,
            HttpServletRequest request) {

        Map<String, Object> resp = new HashMap<>();

        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");

            if (currentLibraryId == null || currentLibraryId == 0) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "请先选择图书馆");
                return ResponseEntity.ok(resp);
            }

            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(resp);
            }

            // 构建更新对象
            LibBook libBook = new LibBook();
            libBook.setLibBookId(libBookId);
            libBook.setLibraryId(currentLibraryId);
            libBook.setTotalCopies(totalCopies);
            libBook.setAvailableCopies(availableCopies);
            libBook.setLoanDays(loanDays);
            libBook.setStatus(status);

            // 执行更新
            ExecuteResult<Boolean> result = libBookService.updateLibBookInfo(libBook);

            if (result.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "更新成功");
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", result.getErrorMessages().get(0));
            }

        } catch (Exception e) {
            log.error("更新图书信息失败, libBookId: {}", libBookId, e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "更新失败：" + e.getMessage());
        }

        return ResponseEntity.ok(resp);
    }

    /**
     * 批量更新图书状态
     */
    @RequestMapping(value = "/api/books/batch-status", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> batchUpdateBookStatus(
            @RequestParam("libBookIds") List<Long> libBookIds,
            @RequestParam("status") Integer status,
            HttpServletRequest request) {

        Map<String, Object> resp = new HashMap<>();

        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");

            if (currentLibraryId == null || currentLibraryId == 0) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "请先选择图书馆");
                return ResponseEntity.ok(resp);
            }

            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(resp);
            }

            // 执行批量更新
            ExecuteResult<Integer> result = libBookService.batchUpdateBookStatus(libBookIds, currentLibraryId, status);

            if (result.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "批量更新成功，共更新 " + result.getResult() + " 本图书");
                resp.put("data", result.getResult());
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", result.getErrorMessages().get(0));
            }

        } catch (Exception e) {
            log.error("批量更新图书状态失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "批量更新失败：" + e.getMessage());
        }

        return ResponseEntity.ok(resp);
    }

    /**
     * 调试用户权限信息
     */
    @RequestMapping(value = "/api/debug-user-info", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> debugUserInfo(HttpServletRequest request) {
        Map<String, Object> resp = new HashMap<>();

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            HttpSession session = request.getSession();
            Collection<? extends GrantedAuthority> authorities = authentication != null ? authentication.getAuthorities() : null;

            // 详细显示角色信息
            List<String> roleStrings = new ArrayList<>();
            if (authorities != null) {
                for (GrantedAuthority auth : authorities) {
                    roleStrings.add(auth.getAuthority());
                }
            }

            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("username", authentication != null ? authentication.getName() : "null");
            userInfo.put("authorities", roleStrings);
            userInfo.put("authoritiesRaw", authorities != null ? authorities.toString() : "null");
            userInfo.put("isAuthenticated", authentication != null ? authentication.isAuthenticated() : false);
            userInfo.put("currentLibraryId", session.getAttribute("currentLibraryId"));
            userInfo.put("sessionId", session.getId());

            resp.put("code", InterfaceContant.ReturnFlag.API_T);
            resp.put("data", userInfo);

        } catch (Exception e) {
            log.error("获取用户调试信息失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "获取用户信息失败：" + e.getMessage());
        }

        return ResponseEntity.ok(resp);
    }

    /**
     * 批量删除图书
     */
    @RequestMapping(value = "/api/books/batch-delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> batchDeleteBooks(
            @RequestParam("libBookIds") List<Long> libBookIds,
            HttpServletRequest request) {

        Map<String, Object> resp = new HashMap<>();

        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");

            log.info("批量删除图书请求 - Session中的图书馆ID: {}, 请求的书籍IDs: {}", currentLibraryId, libBookIds);

            // 检查用户权限，如果是系统管理员且没有选择图书馆，尝试从书籍中获取图书馆ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Collection<? extends GrantedAuthority> authorities = authentication != null ? authentication.getAuthorities() : null;
            boolean isSystemAdmin = authorities != null && authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));

            if (currentLibraryId == null || currentLibraryId == 0) {
                if (isSystemAdmin && !libBookIds.isEmpty()) {
                    // 系统管理员可以从第一个书籍ID获取图书馆ID
                    try {
                        LibBook firstBook = libBookService.queryRecordByPrimaryKey(libBookIds.get(0)).getResult();
                        if (firstBook != null) {
                            currentLibraryId = firstBook.getLibraryId();
                            log.info("系统管理员删除：从书籍ID {} 获取图书馆ID {}", libBookIds.get(0), currentLibraryId);
                        }
                    } catch (Exception e) {
                        log.error("获取书籍图书馆ID失败", e);
                    }
                }

                if (currentLibraryId == null || currentLibraryId == 0) {
                    log.warn("批量删除失败：Session中没有图书馆ID");
                    resp.put("code", InterfaceContant.ReturnFlag.API_F);
                    resp.put("message", "请先选择图书馆");
                    return ResponseEntity.ok(resp);
                }
            }

            // 权限验证
            String username = authentication != null ? authentication.getName() : "null";
            log.info("批量删除权限检查 - 用户: {}, 角色: {}, 图书馆ID: {}", username, authorities, currentLibraryId);

            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                log.warn("批量删除失败：用户 {} 没有图书馆 {} 的权限", username, currentLibraryId);
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(resp);
            }

            // 执行批量删除
            ExecuteResult<Integer> result = libBookService.batchDeleteBooks(libBookIds, currentLibraryId);

            if (result.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", "批量删除成功，共删除 " + result.getResult() + " 本图书");
                resp.put("data", result.getResult());
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", result.getErrorMessages().get(0));
            }

        } catch (Exception e) {
            log.error("批量删除图书失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "批量删除失败：" + e.getMessage());
        }

        return ResponseEntity.ok(resp);
    }

    /**
     * 图书编辑页面
     */
    @RequestMapping(value = "/books/{libBookId}/edit", method = RequestMethod.GET)
    public String bookEditPage(@PathVariable Long libBookId, HttpServletRequest request, ModelMap modelMap) {
        log.debug("访问图书编辑页面, libBookId: {}", libBookId);

        // 获取当前图书馆ID
        HttpSession session = request.getSession();
        Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");

        if (currentLibraryId == null || currentLibraryId == 0) {
            modelMap.addAttribute("errorMessage", "请先选择图书馆");
            return "admin/library/books";
        }

        // 权限验证
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!hasLibraryPermission(authentication, currentLibraryId)) {
            modelMap.addAttribute("errorMessage", "无权限访问该图书馆");
            return "admin/library/books";
        }

        modelMap.addAttribute("libBookId", libBookId);
        modelMap.addAttribute("libraryId", currentLibraryId);

        return "admin/library/book-edit";
    }

    /**
     * 批量导入页面
     */
    @RequestMapping(value = "/books/import", method = RequestMethod.GET)
    public String bookImportPage(HttpServletRequest request, ModelMap modelMap) {
        log.debug("访问批量导入页面");

        // 获取当前图书馆ID
        HttpSession session = request.getSession();
        Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");

        if (currentLibraryId == null || currentLibraryId == 0) {
            modelMap.addAttribute("errorMessage", "请先选择图书馆");
            return "admin/library/books";
        }

        // 权限验证
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!hasLibraryPermission(authentication, currentLibraryId)) {
            modelMap.addAttribute("errorMessage", "无权限访问该图书馆");
            return "admin/library/books";
        }

        modelMap.addAttribute("libraryId", currentLibraryId);

        return "admin/library/book-import";
    }

    /**
     * 下载导入模板
     */
    @RequestMapping(value = "/api/books/import-template", method = RequestMethod.GET)
    public void downloadImportTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=book_import_template.xlsx");

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("图书导入模板");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"书籍ID*", "总库存*", "可用库存*", "借阅天数", "状态", "备注"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 创建示例数据行
            Row exampleRow = sheet.createRow(1);
            exampleRow.createCell(0).setCellValue("1001");
            exampleRow.createCell(1).setCellValue("10");
            exampleRow.createCell(2).setCellValue("10");
            exampleRow.createCell(3).setCellValue("30");
            exampleRow.createCell(4).setCellValue("1");
            exampleRow.createCell(5).setCellValue("上架状态：1，下架状态：0");

            // 创建说明行
            Row noteRow = sheet.createRow(3);
            noteRow.createCell(0).setCellValue("说明：");
            noteRow.createCell(1).setCellValue("1. 书籍ID必须是系统中已存在的图书");

            Row noteRow2 = sheet.createRow(4);
            noteRow2.createCell(1).setCellValue("2. 总库存和可用库存必须为正整数");

            Row noteRow3 = sheet.createRow(5);
            noteRow3.createCell(1).setCellValue("3. 借阅天数为空时使用图书馆默认设置");

            Row noteRow4 = sheet.createRow(6);
            noteRow4.createCell(1).setCellValue("4. 状态：1=上架，0=下架");

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("下载导入模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 批量导入图书
     */
    @RequestMapping(value = "/api/books/import", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> importBooks(
            @RequestParam("importFile") MultipartFile file,
            HttpServletRequest request) {

        Map<String, Object> resp = new HashMap<>();

        try {
            // 获取当前图书馆ID
            HttpSession session = request.getSession();
            Long currentLibraryId = (Long) session.getAttribute("currentLibraryId");

            if (currentLibraryId == null || currentLibraryId == 0) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "请先选择图书馆");
                return ResponseEntity.ok(resp);
            }

            // 权限验证
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (!hasLibraryPermission(authentication, currentLibraryId)) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "无权限访问该图书馆");
                return ResponseEntity.ok(resp);
            }

            // 验证文件
            if (file == null || file.isEmpty()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "请选择要导入的文件");
                return ResponseEntity.ok(resp);
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "请选择Excel文件（.xlsx或.xls格式）");
                return ResponseEntity.ok(resp);
            }

            // 解析Excel文件
            List<LibBookImportDTO> importList = parseExcelFile(file);

            if (importList.isEmpty()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", "文件中没有有效的数据");
                return ResponseEntity.ok(resp);
            }

            // 执行批量导入
            ExecuteResult<Integer> result = libBookService.batchAddBooksToLibrary(currentLibraryId, importList);

            if (result.isSuccess()) {
                resp.put("code", InterfaceContant.ReturnFlag.API_T);
                resp.put("message", result.getSuccessMessage());
                resp.put("data", result.getResult());
            } else {
                resp.put("code", InterfaceContant.ReturnFlag.API_F);
                resp.put("message", result.getErrorMessages().get(0));
            }

        } catch (Exception e) {
            log.error("批量导入图书失败", e);
            resp.put("code", InterfaceContant.ReturnFlag.API_F);
            resp.put("message", "导入失败：" + e.getMessage());
        }

        return ResponseEntity.ok(resp);
    }

    /**
     * 解析Excel文件
     */
    private List<LibBookImportDTO> parseExcelFile(MultipartFile file) throws Exception {
        List<LibBookImportDTO> importList = new ArrayList<>();

        Workbook workbook = null;
        try {
            String fileName = file.getOriginalFilename();
            if (fileName.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(file.getInputStream());
            } else {
                workbook = new HSSFWorkbook(file.getInputStream());
            }

            Sheet sheet = workbook.getSheetAt(0);

            // 跳过标题行，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                // 检查是否为空行
                if (isEmptyRow(row)) {
                    continue;
                }

                LibBookImportDTO importDTO = new LibBookImportDTO();
                importDTO.setRowNumber(i + 1);

                try {
                    // 书籍ID（必填）
                    Cell bookIdCell = row.getCell(0);
                    if (bookIdCell != null) {
                        if (bookIdCell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                            importDTO.setBookId((long) bookIdCell.getNumericCellValue());
                        } else if (bookIdCell.getCellType() == Cell.CELL_TYPE_STRING) {
                            String bookIdStr = bookIdCell.getStringCellValue().trim();
                            if (!bookIdStr.isEmpty()) {
                                importDTO.setBookId(Long.parseLong(bookIdStr));
                            }
                        }
                    }

                    // 总库存（必填）
                    Cell totalCopiesCell = row.getCell(1);
                    if (totalCopiesCell != null) {
                        if (totalCopiesCell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                            importDTO.setTotalCopies((int) totalCopiesCell.getNumericCellValue());
                        } else if (totalCopiesCell.getCellType() == Cell.CELL_TYPE_STRING) {
                            String totalCopiesStr = totalCopiesCell.getStringCellValue().trim();
                            if (!totalCopiesStr.isEmpty()) {
                                importDTO.setTotalCopies(Integer.parseInt(totalCopiesStr));
                            }
                        }
                    }

                    // 可用库存（必填）
                    Cell availableCopiesCell = row.getCell(2);
                    if (availableCopiesCell != null) {
                        if (availableCopiesCell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                            importDTO.setAvailableCopies((int) availableCopiesCell.getNumericCellValue());
                        } else if (availableCopiesCell.getCellType() == Cell.CELL_TYPE_STRING) {
                            String availableCopiesStr = availableCopiesCell.getStringCellValue().trim();
                            if (!availableCopiesStr.isEmpty()) {
                                importDTO.setAvailableCopies(Integer.parseInt(availableCopiesStr));
                            }
                        }
                    }

                    // 借阅天数（可选）
                    Cell loanDaysCell = row.getCell(3);
                    if (loanDaysCell != null) {
                        if (loanDaysCell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                            importDTO.setLoanDays((int) loanDaysCell.getNumericCellValue());
                        } else if (loanDaysCell.getCellType() == Cell.CELL_TYPE_STRING) {
                            String loanDaysStr = loanDaysCell.getStringCellValue().trim();
                            if (!loanDaysStr.isEmpty()) {
                                importDTO.setLoanDays(Integer.parseInt(loanDaysStr));
                            }
                        }
                    }

                    // 状态（可选，默认上架）
                    Cell statusCell = row.getCell(4);
                    if (statusCell != null) {
                        if (statusCell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                            importDTO.setStatus((int) statusCell.getNumericCellValue());
                        } else if (statusCell.getCellType() == Cell.CELL_TYPE_STRING) {
                            String statusStr = statusCell.getStringCellValue().trim();
                            if (!statusStr.isEmpty()) {
                                importDTO.setStatus(Integer.parseInt(statusStr));
                            }
                        }
                    }

                    importList.add(importDTO);

                } catch (Exception e) {
                    log.warn("解析第{}行数据失败: {}", i + 1, e.getMessage());
                    importDTO.setErrorMessage("数据格式错误：" + e.getMessage());
                    importList.add(importDTO);
                }
            }

        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }

        return importList;
    }

    /**
     * 检查是否为空行
     */
    private boolean isEmptyRow(Row row) {
        for (int i = 0; i < 5; i++) { // 检查前5列
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK) {
                String cellValue = "";
                if (cell.getCellType() == Cell.CELL_TYPE_STRING) {
                    cellValue = cell.getStringCellValue().trim();
                } else if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                if (!cellValue.isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }

    // ==================== 内部类定义 ====================
    
    /**
     * 图书馆切换选项
     */
    public static class LibrarySwitchOption {
        private Long libraryId;
        private String libraryName;
        private String libraryKey;
        private String roleType;
        private String icon;
        private String iconColor;
        private String description;
        
        // Getters and Setters
        public Long getLibraryId() { return libraryId; }
        public void setLibraryId(Long libraryId) { this.libraryId = libraryId; }
        
        public String getLibraryName() { return libraryName; }
        public void setLibraryName(String libraryName) { this.libraryName = libraryName; }
        
        public String getLibraryKey() { return libraryKey; }
        public void setLibraryKey(String libraryKey) { this.libraryKey = libraryKey; }
        
        public String getRoleType() { return roleType; }
        public void setRoleType(String roleType) { this.roleType = roleType; }
        
        public String getIcon() { return icon; }
        public void setIcon(String icon) { this.icon = icon; }
        
        public String getIconColor() { return iconColor; }
        public void setIconColor(String iconColor) { this.iconColor = iconColor; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
    
    /**
     * 图书馆切换结果
     */
    public static class LibrarySwitchResult {
        private Long libraryId;
        private String libraryName;
        private String libraryKey;
        private String roleType;
        private Long switchTime;
        
        // Getters and Setters
        public Long getLibraryId() { return libraryId; }
        public void setLibraryId(Long libraryId) { this.libraryId = libraryId; }
        
        public String getLibraryName() { return libraryName; }
        public void setLibraryName(String libraryName) { this.libraryName = libraryName; }
        
        public String getLibraryKey() { return libraryKey; }
        public void setLibraryKey(String libraryKey) { this.libraryKey = libraryKey; }
        
        public String getRoleType() { return roleType; }
        public void setRoleType(String roleType) { this.roleType = roleType; }
        
        public Long getSwitchTime() { return switchTime; }
        public void setSwitchTime(Long switchTime) { this.switchTime = switchTime; }
    }

    
}
