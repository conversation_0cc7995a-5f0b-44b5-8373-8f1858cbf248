<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="csrf-token" content="${_csrf.token}">
    <title>图书馆管理 - <spring:message code="main.title"/></title>
    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <style>
        .chip{display:inline-block;margin:2px 6px 2px 0;padding:2px 8px;border-radius:10px;background:#eef2f7;color:#333;font-size:12px}
        .chip.blue{background:#e8f4ff;color:#0b66c3}
        .chip.orange{background:#fff4e6;color:#d35400}
        .chip.green{background:#e8f8f2;color:#1e8449}

        /* 搜索表单样式优化 */
        .searchform {
            padding: 10px 0;
            max-width: 600px;
        }

        .searchform .form-control {
            margin: 0 5px;
            border-radius: 4px;
            border: 1px solid #ddd;
            padding: 6px 12px;
            font-size: 14px;
        }

        .searchform .btn {
            margin: 0 5px;
            padding: 6px 16px;
            border-radius: 4px;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .searchform {
                flex-direction: column !important;
                align-items: stretch !important;
                gap: 8px !important;
            }

            .searchform .form-control,
            .searchform .btn {
                width: 100% !important;
                margin: 0 !important;
            }
        }
    </style>
    <sec:authorize access="!hasAnyRole('ADMIN','OPERATION')">
        <style>
            /* 对分管理员隐藏ID与标识码列 */
            .col-id, .col-key { display: none; }
        </style>
    </sec:authorize>
    <script src="<c:url value='/js/jquery.min.js'/>"></script>
    <script>
        function doSearch(){
            var params = [];
            var name = document.getElementById('name').value || '';
            var key = document.getElementById('key').value || '';
            var status = document.getElementById('status').value || '';
            if (name) params.push('name='+ encodeURIComponent(name));
            if (key) params.push('key='+ encodeURIComponent(key));
            if (status !== '') params.push('status='+ encodeURIComponent(status));
            var base = '<c:url value="/admin/library/libraries"/>';
            window.location.href = params.length ? (base + '?' + params.join('&')) : base;
        }
        function toEdit(id){
            var url = '<c:url value="/admin/library/library-edit"/>' + (id? ('?id='+id):'');
            window.location.href = url;
        }
        function toCreate(){
            window.location.href = '<c:url value="/admin/library/library-create"/>';
        }
        function manageAdmins(id, name){
            var url = '<c:url value="/admin/library/librarians"/>' + (id? ('?libraryId='+id) : '');
            window.location.href = url;
        }
        function manageUsers(id, name){
            var url = '<c:url value="/admin/library/readers"/>' + (id? ('?libraryId='+id) : '');
            window.location.href = url;
        }
        function manageBookLists(id, name){
            var url = '<c:url value="/admin/library/book-lists"/>' + (id? ('?libraryId='+id) : '');
            window.location.href = url;
        }
    </script>
    <jsp:include page="../footer_js.jsp"/>
    <script src="<c:url value='/js/scripts.min.js'/>"></script>
    <script src="<c:url value='/statics/js/bootstrap.min.js'/>"></script>
    <script src="<c:url value='/statics/js/jquery.dcjqaccordion.2.7.js'/>"></script>
    <script src="<c:url value='/statics/js/common-scripts.js'/>"></script>
</head>
<body class="sticky-header">
<c:set var="roleStringTmp" value="${empty currentUserRolesString ? '' : currentUserRolesString}" />
<c:set var="isManagerRole" value="${fn:contains(roleStringTmp, 'ROLE_ADMIN') or fn:contains(roleStringTmp, 'ROLE_OPERATION')}" />
<section>
    <jsp:include page="../adminLeft.jsp"/>
    <div class="main-content">
        <div class="header-section">
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <form class="searchform form-inline d-flex align-items-center" onsubmit="doSearch();return false;" style="gap: 10px;">
                <input id="name" type="text" class="form-control" placeholder="名称" value="${name}" style="width: 150px;">
                <input id="key" type="text" class="form-control" placeholder="标识码" value="${key}" style="width: 150px;">
                <select id="status" class="form-control" style="width: 120px;">
                    <option value="">状态(全部)</option>
                    <option value="1" <c:if test='${status=="1"}'>selected</c:if>>启用</option>
                    <option value="0" <c:if test='${status=="0"}'>selected</c:if>>停用</option>
                </select>
                <button class="btn btn-primary" type="button" onclick="doSearch()"><i class="fa fa-search"></i> 搜索</button>
            </form>
            <jsp:include page="header_library_switcher.jsp"/>
        </div>

        <div class="page-heading">
            <h3>图书馆列表</h3>
            <ul class="breadcrumb">
                <li><a href="#"><spring:message code="menu.index"/></a></li>
                <li><a href="<c:url value='/admin/library'/>">图书馆后台</a></li>
                <li class="active">图书馆列表</li>
            </ul>
        </div>

        <div class="wrapper">
            <section class="panel">
                <header class="panel-heading">
                    图书馆列表
                    <span class="tools pull-right">
                        <sec:authorize access="hasAnyRole('ADMIN','OPERATION')">
                            <button class="btn btn-primary btn-sm" onclick="toCreate()"><i class="fa fa-plus"></i> 添加图书馆</button>
                        </sec:authorize>
                    </span>
                </header>
                <div class="panel-body">
                    <div class="adv-table">
                        <input type="hidden" id="currentUserRolesData" value="${fn:escapeXml(roleStringTmp)}"/>
                        <table class="table table-striped table-hover table-bordered">
                            <thead>
                            <tr>
                                <th class="col-id" style="width:60px;">ID</th>
                                <th>图书馆名称</th>
                                <th class="col-key">标识码</th>
                                <th>管理员邮箱</th>
                                <th style="width:120px;">最大借阅数</th>
                                <th style="width:120px;">默认借阅天数</th>
                                <th style="width:110px;">馆藏图书</th>
                                <th style="width:280px;">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:if test="${empty list}"><tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr></c:if>
                            <c:forEach var="r" items="${list}">
                                <tr>
                                    <td class="col-id">${r.libraryId}</td>
                                    <td><i class="fa fa-building"></i> ${r.name}</td>
                                    <td class="col-key"><span class="chip orange">${r.key}</span></td>
                                    <td>
                                        <c:forEach var="a" items="${adminMap[r.libraryId]}">
                                            <span class="chip"><i class="fa fa-envelope"></i> ${a.userEmail}</span>
                                        </c:forEach>
                                        <c:if test="${empty adminMap[r.libraryId]}"><span class="text-muted">未设置</span></c:if>
                                    </td>
                                    <td><span class="chip blue">${r.maxBorrowBooks}本</span></td>
                                    <td><span class="chip orange">${r.defaultLoanDays}天</span></td>
                                    <td><span class="chip green">${bookCountMap[r.libraryId]}本</span></td>
                                    <td>
                                        <!-- 编辑按钮：系统管理员和运营管理员可以编辑所有图书馆，图书馆管理员只能编辑自己管理的图书馆 -->
                                        <sec:authorize access="hasAnyRole('ADMIN','OPERATION')">
                                            <button class="btn btn-xs btn-info" title="编辑" onclick="toEdit(${r.libraryId})"><i class="fa fa-edit"></i></button>
                                        </sec:authorize>
                                        <sec:authorize access="hasRole('LIBRARIAN') and !hasAnyRole('ADMIN','OPERATION')">
                                            <c:if test="${canEditMap[r.libraryId]}">
                                                <button class="btn btn-xs btn-info" title="编辑" onclick="toEdit(${r.libraryId})"><i class="fa fa-edit"></i></button>
                                            </c:if>
                                        </sec:authorize>

                                        <!-- 管理员管理：只有系统管理员和运营管理员可以管理 -->
                                        <sec:authorize access="hasAnyRole('ADMIN','OPERATION')">
                                            <button class="btn btn-xs btn-success" title="管理员管理" onclick="manageAdmins(${r.libraryId}, '${r.name}')"><i class="fa fa-user"></i></button>
                                        </sec:authorize>

                                        <!-- 读者管理：所有角色都可以访问 -->
                                        <button class="btn btn-xs btn-primary" title="读者管理" onclick="manageUsers(${r.libraryId}, '${r.name}')"><i class="fa fa-users"></i></button>

                                        <!-- 书单管理：所有角色都可以访问 -->
                                        <button class="btn btn-xs btn-warning" title="书单管理" onclick="manageBookLists(${r.libraryId}, '${r.name}')"><i class="fa fa-list"></i></button>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                        <div>${pageLand}</div>
                    </div>
                </div>
            </section>
        </div>

        <jsp:include page="../footer_show.jsp"/>
    </div>
</section>
<script>
    $(function(){
        $(".menu-list").removeClass("nav-active");
        $(".menu-list li").removeClass("active");
        $("#menu_library").addClass("nav-active");
        $("#librariesList").addClass("active");

        var roleString = $("#currentUserRolesData").val() || "";
        var roles = roleString.split(",").map(function(item){ return $.trim(item); }).filter(function(item){ return item.length > 0; });
        var isManager = roles.indexOf("ROLE_ADMIN") !== -1 || roles.indexOf("ROLE_OPERATION") !== -1;
        if (!isManager) {
            // 非管理员隐藏ID与标识码列
            var sensitiveColumns = [1, 3];
            var $table = $(".adv-table table");
            sensitiveColumns.forEach(function(idx){
                var selector = "th:nth-child(" + idx + "), td:nth-child(" + idx + ")";
                $table.find(selector).each(function(){
                    var $cell = $(this);
                    if ($cell.is("td")) { $cell.text("--"); }
                    $cell.hide();
                });
            });
        }
    });
    </script>
</body>
</html>

