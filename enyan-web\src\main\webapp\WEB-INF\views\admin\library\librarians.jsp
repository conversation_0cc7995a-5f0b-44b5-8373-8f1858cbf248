<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="csrf-token" content="${_csrf.token}">
    <title>图书馆管理员 - <spring:message code="main.title"/></title>
    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
</head>
<body class="sticky-header">
<section>
    <jsp:include page="../adminLeft.jsp"/>
    <div class="main-content">
        <div class="header-section">
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <form class="searchform" onsubmit="return false;">
                <div class="input-group">
                    <input id="keyword" type="text" class="form-control" placeholder="账号/图书馆名称" value="${keyword}">
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" onclick="doSearch()"><i class="fa fa-search"></i></button>
                    </span>
                </div>
            </form>
            <jsp:include page="header_library_switcher.jsp"/>
        </div>

        <div class="page-heading">
            <h3>图书馆管理员</h3>
            <ul class="breadcrumb">
                <li><a href="#"><spring:message code="menu.index"/></a></li>
                <li><a href="<c:url value='/admin/library' />">图书馆管理</a></li>
                <li class="active">图书馆管理员</li>
            </ul>
        </div>

        <div class="wrapper">
            <section class="panel">
                <header class="panel-heading">管理员列表
                    <span class="tools pull-right">
                        <button class="btn btn-primary btn-sm" onclick="openAddModal()"><i class="fa fa-plus"></i> 添加管理员</button>
                    </span>
                </header>
                <div class="panel-body">
                    <div class="adv-table">
                        <table class="table table-striped table-hover table-bordered" id="librarians-table">
                            <thead>
                                <tr>
                                    <th style="width:220px;">图书馆</th>
                                    <th>管理员账号</th>
                                    <th style="width:160px;">创建时间</th>
                                    <th style="width:90px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="librarians-tbody">
                                <c:if test="${empty list}">
                                    <tr><td colspan="4" class="text-center text-muted">暂无数据</td></tr>
                                </c:if>
                                <c:forEach var="entry" items="${grouped}">
                                    <c:set var="libName" value="${entry.key}"/>
                                    <c:set var="rows" value="${entry.value}"/>
                                    <c:forEach var="r" items="${rows}" varStatus="status">
                                        <tr>
                                            <c:if test="${status.index == 0}">
                                                <td rowspan="${fn:length(rows)}">${libName}</td>
                                            </c:if>
                                            <td><a href="javascript:void(0)" onclick="showLibrarianDetail(${r.libUserId})" class="text-primary">${r.userEmail}</a></td>
                                            <td><fmt:formatDate value="${r.createdAt}" pattern="yyyy-MM-dd"/></td>
                                            <td class="text-center">
                                                <a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="deleteLibrarian(${r.libUserId})">
                                                    <i class="fa fa-trash-o"></i> 删除
                                                </a>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    <!--pagination start-->
                    <div class="">
                        ${pageLand}
                    </div>
                    <!--pagination end-->
                </div>

                <!-- 添加管理员 Modal -->
                <div class="modal fade" id="addLibrarianModal" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                                <h4 class="modal-title">添加图书馆管理员</h4>
                            </div>
                            <div class="modal-body">
                                <form id="addLibrarianForm" onsubmit="return false;">
                                    <div class="form-group">
                                        <label>选择图书馆 *</label>
                                        <div class="custom-select-wrapper">
                                            <select id="librarySelect" class="form-control custom-select"></select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>管理员账号 *</label>
                                        <input id="librarianAccount" type="text" class="form-control" placeholder="***************或****************" />
                                        <span class="help-block">请输入符合邮箱格式的账号，可使用@lib.local等本地域名，添加后立即生效</span>
                                    </div>
                                    <div class="form-group">
                                        <label>管理员姓名</label>
                                        <input id="librarianDisplayName" type="text" class="form-control" placeholder="可选，用于显示" />
                                        <span class="help-block">可选填，用于在界面上显示管理员姓名</span>
                                    </div>
                                    <div class="form-group">
                                        <label>初始密码</label>
                                        <div class="input-group">
                                            <input id="librarianPassword" type="password" class="form-control" placeholder="留空则使用默认密码" />
                                            <span class="input-group-addon" style="cursor: pointer;" onclick="togglePasswordVisibility()">
                                                <i id="passwordToggleIcon" class="fa fa-eye-slash"></i>
                                            </span>
                                        </div>
                                        <span class="help-block">密码要求：至少8位，包含大小写字母、数字和特殊字符。留空将使用默认密码：LibAdmin123!</span>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="submitAddLibrarian()">添加管理员</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <jsp:include page="../footer_show.jsp"/>
    </div>
</section>

<!-- 管理员详细信息 Modal -->
<div class="modal fade" id="librarianDetailModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">管理员详细信息</h4>
            </div>
            <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>所属图书馆：</label>
                                <p id="detailLibraryName" class="form-control-static"></p>
                            </div>
                        </div>
                        <sec:authorize access="hasAnyRole('ADMIN','OPERATION')">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>图书馆代码：</label>
                                    <p id="detailLibraryKey" class="form-control-static"></p>
                                </div>
                            </div>
                        </sec:authorize>
                    </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>管理员账号：</label>
                            <p id="detailUserEmail" class="form-control-static"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>管理员姓名：</label>
                            <p id="detailDisplayName" class="form-control-static"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>创建时间：</label>
                            <p id="detailCreatedAt" class="form-control-static"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<jsp:include page="../footer_js.jsp"/>
<script>
    function doSearch(){
        var kw = encodeURIComponent($('#keyword').val() || '');
        var base = '<c:url value="/admin/library/librarians"/>';
        if (kw){ window.location.href = base + '?keyword=' + kw; } else { window.location.href = base; }
    }

    function openAddModal(){
        // 先加载图书馆列表
        loadLibraries(function(){
            $('#librarianAccount').val('');
            $('#librarianDisplayName').val('');
            $('#librarianPassword').val('');
            $('#addLibrarianModal').modal('show');
            
            // 解决select下拉框被遮挡的问题
            $('#librarySelect').on('focus', function() {
                // 当select获得焦点时，确保模态框有足够空间显示下拉选项
                var modal = $('#addLibrarianModal');
                var modalDialog = modal.find('.modal-dialog');
                modalDialog.css({
                    'margin-top': '30px',
                    'margin-bottom': '30px'
                });
            });
        });
    }

    function loadLibraries(done){
        var sel = $('#librarySelect');
        sel.empty();
        $.get('<c:url value="/admin/library/api/switchable-libraries"/>').done(function(resp){
            sel.append('<option value="">请选择图书馆</option>');
            if (resp && resp.data){
                resp.data.forEach(function(item){
                    if (item.roleType === 'LIBRARY_ADMIN'){
                        sel.append('<option value="'+ item.libraryId +'">'+ item.libraryName +'</option>');
                    }
                });
            }
            if (done) done();
        });
    }

    function submitAddLibrarian(){
        var libId = $('#librarySelect').val();
        var account = $('#librarianAccount').val();
        var displayName = $('#librarianDisplayName').val();
        var password = $('#librarianPassword').val();

        console.log('libId:', libId, 'account:', account, 'displayName:', displayName, 'password:', password ? '***' : 'empty');

        if (!libId){ showNotification('请选择图书馆', 'error'); return; }
        if (!account){ showNotification('请输入管理员账号', 'error'); return; }

        $.ajax({
            url: '<c:url value="/admin/library/api/librarians"/>',
            type: 'POST',
            data: {
                libraryId: libId,
                userEmail: account,
                displayName: displayName,
                password: password,
                '${_csrf.parameterName}': '${_csrf.token}'
            }
        })
        .done(function(resp){
            if (resp && (resp.code === 'T' || resp.code === 200 || resp.code === 0)){
                $('#addLibrarianModal').modal('hide');
                showNotification('添加成功', 'success');
                // 刷新当前页
                window.location.reload();
            } else {
                showNotification(resp.message || '添加失败', 'error');
            }
        }).fail(function(){ showNotification('网络错误', 'error'); });
    }

    function deleteLibrarian(id){
        if (!id) return;
        if (!confirm('确认删除该管理员？')) return;
        $.ajax({
            url: '<c:url value="/admin/library/api/librarians"/>/'+ id +'/delete',
            type: 'POST',
            data: {
                '${_csrf.parameterName}': '${_csrf.token}'
            }
        })
        .done(function(resp){
            if (resp && (resp.code === 'T' || resp.code === 200 || resp.code === 0)){
                showNotification('删除成功', 'success');
                window.location.reload();
            } else {
                showNotification(resp.message || '删除失败', 'error');
            }
        }).fail(function(){ showNotification('网络错误', 'error'); });
    }

    function showLibrarianDetail(libUserId){
        if (!libUserId) return;
        $.get('<c:url value="/admin/library/api/librarians"/>/' + libUserId)
        .done(function(resp){
            if (resp && (resp.code === 'T' || resp.code === 200 || resp.code === 0) && resp.data){
                var data = resp.data;
                $('#detailLibraryName').text(data.libraryName || '');
                $('#detailLibraryKey').text(data.libraryKey || '');
                $('#detailUserEmail').text(data.userEmail || '');
                $('#detailDisplayName').text(data.displayName || '暂无');

                if (data.createdAt) {
                    var date = new Date(data.createdAt);
                    $('#detailCreatedAt').text(date.getFullYear() + '-' +
                        String(date.getMonth() + 1).padStart(2, '0') + '-' +
                        String(date.getDate()).padStart(2, '0'));
                } else {
                    $('#detailCreatedAt').text('');
                }

                $('#librarianDetailModal').modal('show');
            } else {
                showNotification(resp.message || '获取详细信息失败', 'error');
            }
        }).fail(function(){
            showNotification('网络错误', 'error');
        });
    }

    function showNotification(message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
            message +
            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '</div>');

        $('body').append(notification);

        // 自动移除通知
        setTimeout(function() {
            notification.alert('close');
        }, 3000);
    }

    function togglePasswordVisibility(){
        var passwordField = $('#librarianPassword');
        var toggleIcon = $('#passwordToggleIcon');

        if (passwordField.attr('type') === 'password') {
            // 切换为明文显示
            passwordField.attr('type', 'text');
            toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
        } else {
            // 切换为密码隐藏
            passwordField.attr('type', 'password');
            toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
        }
    }

    $(function(){
        // 激活菜单
        $(".menu-list").removeClass("nav-active");
        $(".menu-list li").removeClass("active");
        $("#menu_library").addClass("nav-active");
        $("#libraryLibrarians").addClass("active");
    });
</script>
</body>
</html>
