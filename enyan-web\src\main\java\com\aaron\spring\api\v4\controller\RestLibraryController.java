package com.aaron.spring.api.v4.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.api.v4.model.*;
import com.aaron.spring.model.*;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.LibLibraryService;
import com.aaron.spring.service.LibBookService;
import com.aaron.spring.service.LibBorrowService;
import com.aaron.spring.service.LibUserService;
import com.aaron.spring.service.LibReservationService;
import com.aaron.spring.service.LibBookListService;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.common.Constant;
import com.aaron.util.ExecuteResult;
import com.aaron.exception.DownloadException;
import com.aaron.api.constant.InterfaceContant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: James Zhai
 * @Description: 图书馆API控制器
 * @Date: Created in  2025/7/10
 * @Modified By:
 */
@Slf4j
@RestController("RestLibraryControllerV4")
@RequestMapping("/api/v4/library")
@Api(tags = "Library API v4 图书馆相关接口")
public class RestLibraryController extends RestBaseController {

    @Autowired
    private LibLibraryService libLibraryService;
    
    @Autowired
    private LibBookService libBookService;
    
    @Autowired
    private LibBorrowService libBorrowService;
    
    @Autowired
    private LibUserService libUserService;
    
    @Autowired
    private LibReservationService libReservationService;
    

    @Autowired
    private LibBookListService libBookListService;
    
    @Autowired
    private EnyanBookService enyanBookService;

    /**
     * 获取当前用户加入的机构/图书馆列表
     *
     * @param restLibrary 请求参数
     * @param request     HTTP请求
     * @return 用户加入的图书馆分页列表
     */
    @RequestMapping(value = "/me", method = RequestMethod.POST)
    @Operation(summary = "获取当前用户加入的机构/图书馆列表",
            description = "## 功能描述\n" +
                    "获取当前登录用户已加入的所有机构/图书馆列表，支持分页查询。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/me\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| page | Integer | 是 | 当前页码，从1开始 | 1 |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,                     // 响应码：0-成功，非0-失败\n" +
                    "  \"message\": \"查询成功\",         // 响应消息\n" +
                    "  \"result\": [                    // 分页结果\n" +
                    "    {\n" +
                    "      \"libraryId\": 123,          // 图书馆ID\n" +
                    "      \"name\": \"中央图书馆\",      // 图书馆名称\n" +
                    "      \"description\": \"综合性图书馆，藏书丰富\",  // 图书馆描述\n" +
                    "      \"key\": \"central_lib\",    // 图书馆标识键\n" +
                    "      \"status\": 1,               // 状态：0-禁用，1-启用\n" +
                    "      \"createdAt\": \"2023-01-01T00:00:00\"  // 创建时间\n" +
                    "    },\n" +
                    "    // 更多图书馆记录...\n" +
                    "  ],\n" +
                    "  \"pageSize\": 10,                // 每页记录数\n" +
                    "  \"currentPage\": 1,              // 当前页码\n" +
                    "  \"totalRecord\": 25              // 总记录数\n" +
                    "}\n" +
                    "```\n\n" +
                    "## 响应状态码说明\n" +
                    "| 状态码 | 描述 | 可能原因 |\n" +
                    "|--------|------|----------|\n" +
                    "| 200 | 成功 | 请求成功处理 |\n" +
                    "| 400 | 请求参数错误 | 缺少必要参数或参数格式错误 |\n" +
                    "| 401 | 未授权 | 用户未登录或token无效 |\n" +
                    "| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取用户加入的图书馆列表",
                    content = @Content(schema = @Schema(implementation = PageResult.class)))
    })
    public PageResult<RestLibrary> getUserLibraryList(
            @Parameter(description = "请求参数，包含分页信息", required = true)
            @RequestBody RestLibrary restLibrary,
            HttpServletRequest request) {
        restLibrary.initHeaderValue(request);

        PageResult<RestLibrary> pageResult = new PageResult<>();
        if (restLibrary.getPage() == null) {
            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }

        // 获取当前用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            pageResult.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return pageResult;
        }

        Page<LibLibrary> page = new Page<>();
        page.setCurrentPage(restLibrary.getPage());
        page.setPageSize(pageResult.getPageSize());

        // 查询用户加入的图书馆列表
        page = libLibraryService.queryUserJoinedLibraries(page, userEmail);

        // 转换为前端模型
        for (LibLibrary library : page.getRecords()) {
            RestLibrary restLib = new RestLibrary();
            restLib.initFrom(library);
            pageResult.getResult().add(restLib);
        }

        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());

        return pageResult;
    }

    /**
     * 获取图书馆书籍列表
     *
     * @param libraryId   图书馆ID
     * @param restLibBook 请求参数
     * @param request     HTTP请求
     * @return 书籍分页列表
     */
    @RequestMapping(value = "/books", method = RequestMethod.POST)
    @Operation(summary = "获取图书馆书籍列表",
            description = "## 功能描述\n" +
                    "获取图书馆的书籍信息。根据是否传入筛选条件（title、author、isbn、categoryId）决定返回格式：\n" +
                    "- **无筛选条件**：返回首页格式，包含分类书籍(shopTopCategories)，每个分类显示6本书（editorList推荐功能已移除，如需书单功能请使用/api/v4/library/booklists接口）\n" +
                    "- **有筛选条件**：返回分页列表格式，正常的书籍分页数据\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/books\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 123 |\n" +
                    "| page | Integer | 是 | 当前页码，从1开始 | 1 |\n" +
                    "| title | String | 否 | 书籍标题模糊查询（传入则返回分页格式） | Java编程思想 |\n" +
                    "| author | String | 否 | 作者名称模糊查询（传入则返回分页格式） | 李刚 |\n" +
                    "| isbn | String | 否 | 书籍ISBN精确查询（传入则返回分页格式） | 9787111128060 |\n" +
                    "| categoryId | Long | 否 | 分类ID查询（传入则返回分页格式） | 1 |\n" +
                    "| orderBy | Integer | 否 | 排序方式：0-推荐排序（默认），1-最新上架，2-创建时间 | 0 |\n\n" +
                    "## 响应示例\n" +
                    "### 首页格式（无筛选条件时）：\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 200,\n" +
                    "  \"result\": {\n" +
                    "    \"editorList\": [ /* 空数组，推荐功能已移除，请使用/booklists接口 */ ],\n" +
                    "    \"shopTopCategories\": [ /* 分类书籍列表，每类6本 */ ]\n" +
                    "  }\n" +
                    "}\n" +
                    "```\n\n" +
                    "### 分页格式（有筛选条件时）：\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,                     // 响应码：0-成功，非0-失败\n" +
                    "  \"message\": \"查询成功\",         // 响应消息\n" +
                    "  \"result\": [                    // 分页结果\n" +
                    "    {\n" +
                    "      \"bookId\": 456,            // 书籍ID\n" +
                    "      \"title\": \"Java编程思想\",  // 书籍标题\n" +
                    "      \"author\": \"Bruce Eckel\",  // 作者\n" +
                    "      \"publisher\": \"机械工业出版社\",  // 出版社\n" +
                    "      \"isbn\": \"9787111128060\",  // ISBN号\n" +
                    "      \"publishDate\": \"2007-06-01\",  // 出版日期\n" +
                    "      \"coverImage\": \"https://example.com/covers/9787111128060.jpg\",  // 封面图URL\n" +
                    "      \"availableCopies\": 5,     // 可借数量\n" +
                    "      \"totalCopies\": 10,        // 总数量\n" +
                    "      \"location\": \"A区3排2号\",  // 所在位置\n" +
                    "      \"status\": 1,              // 状态：0-下架，1-上架\n" +
                    "      \"createdAt\": \"2023-01-01T00:00:00\"  // 创建时间\n" +
                    "    },\n" +
                    "    // 更多书籍记录...\n" +
                    "  ],\n" +
                    "  \"pageSize\": 10,                // 每页记录数\n" +
                    "  \"currentPage\": 1,              // 当前页码\n" +
                    "  \"totalRecord\": 100             // 总记录数\n" +
                    "}\n" +
                    "```\n\n" +
                    "## 响应状态码说明\n" +
                    "| 状态码 | 描述 | 可能原因 |\n" +
                    "|--------|------|----------|\n" +
                    "| 200 | 成功 | 请求成功处理 |\n" +
                    "| 400 | 请求参数错误 | 缺少必要参数或参数格式错误 |\n" +
                    "| 401 | 未授权 | 用户未登录或token无效 |\n" +
                    "| 403 | 禁止访问 | 用户无权限访问该图书馆 |\n" +
                    "| 404 | 资源不存在 | 指定的图书馆ID不存在 |\n" +
                    "| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取书籍列表",
                    content = @Content(schema = @Schema(implementation = PageResult.class)))
    })
    public ExecuteResult<Object> getLibraryBooks(
            @Parameter(description = "请求参数，包含图书馆ID、分页和筛选条件", required = true)
            @RequestBody RestLibBook restLibBook,
            HttpServletRequest request) {
        restLibBook.initHeaderValue(request);

        ExecuteResult<Object> result = new ExecuteResult<>();
        
        if (restLibBook.getLibraryId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_ID_REQUIRED);
            return result;
        }
        
        if (restLibBook.getPage() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        // 检查是否有筛选条件
        boolean hasFilters = StringUtils.isNotBlank(restLibBook.getTitle()) || 
                           StringUtils.isNotBlank(restLibBook.getAuthor()) ||
                           StringUtils.isNotBlank(restLibBook.getIsbn()) ||
                           restLibBook.getCategoryId() != null;

        if (hasFilters) {
            // 有筛选条件：返回分页格式
            PageResult<RestLibBook> pageResult = getLibraryBooksWithFilters(restLibBook);
            result.setResult(pageResult);
        } else {
            // 无筛选条件：返回首页格式
            RestShopIndex shopIndex = getLibraryHomeIndex(restLibBook.getLibraryId());
            result.setResult(shopIndex);
        }

        return result;
    }

    /**
     * 获取带筛选条件的图书馆书籍分页列表
     */
    private PageResult<RestLibBook> getLibraryBooksWithFilters(RestLibBook restLibBook) {
        PageResult<RestLibBook> pageResult = new PageResult<>();
        
        Page<LibBookWithDetail> page = new Page<>();
        page.setCurrentPage(restLibBook.getPage());
        page.setPageSize(pageResult.getPageSize());

        // 暂时使用内存筛选，后续可优化为数据库层筛选
        page = libBookService.queryBooksWithDetailByLibraryId(page, restLibBook.getLibraryId(), restLibBook.getOrderBy());
        
        // 在内存中进行筛选
        List<LibBookWithDetail> filteredBooks = new ArrayList<>();
        for (LibBookWithDetail libBook : page.getRecords()) {
            boolean matches = true;
            
            // 标题筛选
            if (StringUtils.isNotBlank(restLibBook.getTitle()) && 
                (libBook.getBookTitle() == null || !libBook.getBookTitle().toLowerCase().contains(restLibBook.getTitle().toLowerCase()))) {
                matches = false;
            }
            
            // 作者筛选
            if (matches && StringUtils.isNotBlank(restLibBook.getAuthor()) && 
                (libBook.getBookAuthor() == null || !libBook.getBookAuthor().toLowerCase().contains(restLibBook.getAuthor().toLowerCase()))) {
                matches = false;
            }
            
            // ISBN筛选
            if (matches && StringUtils.isNotBlank(restLibBook.getIsbn()) && 
                (libBook.getBookIsbn() == null || !libBook.getBookIsbn().equals(restLibBook.getIsbn()))) {
                matches = false;
            }
            
            // 分类ID筛选
            if (matches && restLibBook.getCategoryId() != null && 
                !restLibBook.getCategoryId().equals(libBook.getBookCategoryId())) {
                matches = false;
            }
            
            if (matches) {
                filteredBooks.add(libBook);
            }
        }
        
        page.setRecords(filteredBooks);
        page.setTotalRecord(filteredBooks.size());

        // 转换为前端模型
        for (LibBookWithDetail libBook : page.getRecords()) {
            RestLibBook restBook = new RestLibBook();
            restBook.initFrom(libBook);
            pageResult.getResult().add(restBook);
        }

        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());
        
        return pageResult;
    }

    /**
     * 获取图书馆首页格式数据
     */
    private RestShopIndex getLibraryHomeIndex(Long libraryId) {
        // 确保普通书城的分类数据已初始化
        if (Constant.indexCategoryList == null || Constant.indexCategoryList.isEmpty()) {
            enyanBookService.initIndexAllInfo();
        }
        
        RestShopIndex shopIndex = new RestShopIndex();
        
        // 1. editorList (推荐书籍) - 已移除推荐功能，如需书单功能请使用 /api/v4/library/booklists 接口
        List<RestBook> editorList = new ArrayList<>();
        shopIndex.setEditorList(editorList);
        
        // 2. 获取分类书籍 (shopTopCategories) - 核心功能
        List<RestShopCategory> shopTopCategories = new ArrayList<>();
        
        if (Constant.indexCategoryList != null) {
            // 复用普通书城的分类结构，但只包含图书馆有藏书的分类
            for (EnyanCategory category : Constant.indexCategoryList) {
                RestShopCategory shopCategory = new RestShopCategory();
                shopCategory.setCategoryId(category.getCategoryId());
                shopCategory.setCategoryNameSc(category.getCategoryName());
                shopCategory.setCategoryNameTc(category.getCategoryNameTc());
                shopCategory.setCategoryNameEn(category.getCategoryNameEn());
                shopCategory.setBannerUrl(category.getBannerUrl());
                shopCategory.setBookCoverUrl(category.getBookCoverUrl());
                
                // 获取该分类在图书馆的书籍（最多6本，与普通书城保持一致）
                List<RestBook> categoryBooks = getLibraryCategoryBooks(libraryId, category.getCategoryId(), 6);
                if (!categoryBooks.isEmpty()) {
                    shopCategory.setRecords(categoryBooks);
                    shopTopCategories.add(shopCategory);
                }
            }
        }
        shopIndex.setShopTopCategories(shopTopCategories);
        
        return shopIndex;
    }

    /**
     * 获取图书馆某分类的书籍
     */
    private List<RestBook> getLibraryCategoryBooks(Long libraryId, Long categoryId, int limit) {
        List<RestBook> books = new ArrayList<>();
        
        try {
            // 使用分页查询获取指定数量的书籍
            Page<LibBookWithDetail> page = new Page<>();
            page.setCurrentPage(1);
            page.setPageSize(limit);
            
            // 调用现有服务获取图书馆书籍，然后根据分类ID筛选
            page = libBookService.queryBooksWithDetailByLibraryId(page, libraryId, 0);
            
            // 筛选指定分类的书籍并转换格式
            for (LibBookWithDetail libBook : page.getRecords()) {
                if (categoryId.equals(libBook.getBookCategoryId())) {
                    RestBook restBook = new RestBook();
                    restBook.setBookId(libBook.getBookId());
                    restBook.setName(libBook.getBookTitle());
                    restBook.setAuthor(libBook.getBookAuthor());
                    restBook.setBookDescription(libBook.getBookDescription());
                    restBook.setImgUrl(libBook.getBookCoverUrl());
                    restBook.setPublisherName(libBook.getBookPublisher());
                    restBook.setIsbn(libBook.getBookIsbn());
                    // RestBook没有pages字段
                    restBook.setCategoryId(libBook.getBookCategoryId());
                    
                    books.add(restBook);
                    
                    // 达到限制数量就停止
                    if (books.size() >= limit) {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取图书馆分类书籍失败: libraryId={}, categoryId={}", libraryId, categoryId, e);
        }
        
        return books;
    }

    /**
     * 借书功能
     *
     * @param borrowRequest 借书请求
     * @param request       HTTP请求
     * @return 借书结果
     */
    @RequestMapping(value = "/books/borrow", method = RequestMethod.POST)
    @Operation(summary = "借书功能",
            description = "## 功能描述\n" +
                    "用户借阅指定图书馆中的书籍。系统会检查用户借阅权限、书籍可借状态，并处理借阅逻辑。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/books/borrow\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 123 |\n" +
                    "| libBookId | Long | 是 | 图书馆藏书ID（在图书馆中的副本ID） | 456 |\n\n" +
                    "## 响应状态码\n" +
                    "| 状态码 | 描述 | 可能原因 |\n" +
                    "|--------|------|----------|\n" +
                    "| 200 | 成功 | 借书操作成功完成 |\n" +
                    "| 400 | 请求参数错误 | 缺少必要参数或参数格式错误 |\n" +
                    "| 401 | 未授权 | 用户未登录或token无效 |\n" +
                    "| 403 | 禁止访问 | 用户没有借阅权限 |\n" +
                    "| 404 | 资源不存在 | 指定的图书馆或书籍不存在 |\n" +
                    "| 409 | 资源冲突 | 书籍已被借出或不可用 |\n" +
                    "| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "借书操作完成",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<String> borrowBook(
            @Parameter(description = "借书请求参数", required = true)
            @RequestBody RestBorrowRequest borrowRequest,
            HttpServletRequest request) {
        borrowRequest.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();

        // 参数验证
        if (borrowRequest.getLibraryId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_ID_REQUIRED);
            return result;
        }
        if (borrowRequest.getLibBookId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_BOOK_NOT_EXIST);
            return result;
        }

        // 获取用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 执行借书操作
        ExecuteResult<LibBorrow> borrowResult = libBorrowService.borrowBook(
                borrowRequest.getLibraryId(),
                borrowRequest.getLibBookId(),
                userEmail
        );

        if (borrowResult.isSuccess()) {
            result.setSuccessMessage(ReturnInfo.SUCCESS_BOOK_BORROWED);
            result.setResult("借书成功，借阅记录ID: " + borrowResult.getResult().getLibBorrowId());
        } else {
            result.setErrorMessages(borrowResult.getErrorMessages());
        }

        return result;
    }

    /**
     * 还书功能
     *
     * @param borrowRequest 还书请求
     * @param request       HTTP请求
     * @return 还书结果
     */
    @RequestMapping(value = "/books/return", method = RequestMethod.POST)
    @Operation(summary = "还书功能",
            description = "## 功能描述\n" +
                    "用户归还指定图书馆的书籍。系统会验证借阅记录、更新书籍库存，并计算是否产生滞纳金。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/books/return\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libBorrowId | Long | 是 | 借阅记录ID | 789 |\n" +
                    "| libraryId | Long | 否 | 图书馆ID（可选，用于额外验证） | 123 |\n" +
                    "| libBookId | Long | 否 | 图书馆中的书籍ID（可选，用于额外验证） | 456 |\n\n" +
                    "## 响应状态码\n" +
                    "| 状态码 | 描述 | 可能原因 |\n" +
                    "|--------|------|----------|\n" +
                    "| 200 | 成功 | 还书操作成功完成 |\n" +
                    "| 400 | 请求参数错误 | 缺少必要参数或参数格式错误 |\n" +
                    "| 401 | 未授权 | 用户未登录或token无效 |\n" +
                    "| 403 | 禁止访问 | 用户没有操作该借阅记录的权限 |\n" +
                    "| 404 | 资源不存在 | 指定的借阅记录不存在 |\n" +
                    "| 409 | 资源冲突 | 书籍已被归还或状态异常 |\n" +
                    "| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "还书操作完成",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<String> returnBook(
            @Parameter(description = "还书请求参数", required = true)
            @RequestBody RestBorrowRequest borrowRequest,
            HttpServletRequest request) {
        borrowRequest.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();

        // 参数验证
        if (borrowRequest.getLibBorrowId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_BORROW_NOT_EXIST);
            return result;
        }

        // 获取用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 执行还书操作
        ExecuteResult<Boolean> returnResult = libBorrowService.returnBook(
                borrowRequest.getLibBorrowId(),
                userEmail
        );

        if (returnResult.isSuccess()) {
            result.setSuccessMessage(ReturnInfo.SUCCESS_BOOK_RETURNED);
            result.setResult("还书成功");
        } else {
            result.setErrorMessages(returnResult.getErrorMessages());
        }

        return result;
    }

    /**
     * 上传图书馆书籍阅读进度/分组
     */
    @RequestMapping(value = "/uploadRead", method = RequestMethod.POST)
    @Operation(summary = "上传图书馆书籍阅读进度/分组",
            description = "## 功能描述\n" +
                    "用户在图书馆书籍的阅读进度与分组上报。该接口与普通书籍的/uploadRead一致，写入lib_borrow表的read_info/read_time及group_name/group_name_time。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/uploadRead\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer xxx |\n" +
                    "| email | String | 是 | 用户邮箱（由拦截器注入） | <EMAIL> |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 1 |\n" +
                    "| libBookId | Long | 是 | 图书馆书籍ID | 1001 |\n" +
                    "| readLocation | String | 否 | 阅读位置字符串（与/ book一致），若过长(>500)将被丢弃 | {\"cfi\":\"epubcfi(/6/4[chapter01]!/4/2/2[para05]/3:10)\"}   |\n" +
                    "| groupName | String | 否 | 书籍分组名 | 技术类 |\n\n" +
                    "说明：readLocation与groupName至少一个非空。\n\n" +
                    "## 响应说明\n" +
                    "成功时返回回填的时间戳（readTime/groupNameTime），并清空回传的readLocation/groupName明文。\n\n" +
                    "## 响应状态码\n" +
                    "| 状态码 | 描述 | 可能原因 |\n" +
                    "|--------|------|----------|\n" +
                    "| 200 | 成功 | 上报成功 |\n" +
                    "| 400 | 参数错误 | 缺少必填项或readLocation长度>500 |\n" +
                    "| 401 | 未授权 | 用户未登录或邮箱无效 |")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "上报完成",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<RestLibBook> uploadRead(
            @RequestBody RestLibBook restObj,
            HttpServletRequest request) {
        ExecuteResult<RestLibBook> result = new ExecuteResult<>();
        restObj.initHeaderValue(request);

        String email = getRestEmail(request);
        if (StringUtils.isBlank(email)){
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }
        if (restObj.getLibraryId() == null || restObj.getLibBookId() == null || restObj.getLibBookId() <= 0){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (StringUtils.isBlank(restObj.getReadLocation())
                && StringUtils.isBlank(restObj.getGroupName())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (StringUtils.isNotBlank(restObj.getReadLocation())
                && restObj.getReadLocation().length() > 500){
            restObj.setReadLocation(null);
            restObj.setGroupName(null);
            result.setResult(restObj);
            return result;
        }
        Long currentTime = System.currentTimeMillis();

        LibBorrow borrow = new LibBorrow();
        borrow.setUserEmail(email);
        borrow.setLibraryId(restObj.getLibraryId());
        borrow.setLibBookId(restObj.getLibBookId());

        if (StringUtils.isNotBlank(restObj.getReadLocation())
                && StringUtils.isNotBlank(restObj.getGroupName())){
            borrow.setReadInfo(restObj.getReadLocation());
            borrow.setReadTime(currentTime);
            borrow.setGroupName(restObj.getGroupName());
            borrow.setGroupNameTime(currentTime);

            restObj.setReadTime(currentTime);
            restObj.setGroupNameTime(currentTime);
            libBorrowService.updateReadInfoAndGroupNameByEmailAndBookId(borrow);
        }else if (StringUtils.isNotBlank(restObj.getReadLocation())){
            borrow.setReadInfo(restObj.getReadLocation());
            borrow.setReadTime(currentTime);
            restObj.setReadTime(currentTime);
            libBorrowService.updateReadInfoByEmailAndBookId(borrow);
        }else if (StringUtils.isNotBlank(restObj.getGroupName())){
            borrow.setGroupName(restObj.getGroupName());
            borrow.setGroupNameTime(currentTime);
            restObj.setGroupNameTime(currentTime);
            libBorrowService.updateGroupNameByEmailAndBookId(borrow);
        }

        restObj.setReadLocation(null);
        restObj.setGroupName(null);
        result.setResult(restObj);
        return result;
    }

    /**
     * 查询用户是否加入了某个图书馆，以及加入状态
     *
     * @param libraryId 图书馆ID
     * @param request   HTTP请求
     * @return 用户加入状态
     */
    @RequestMapping(value = "/join-status", method = RequestMethod.POST)
    @Operation(summary = "查询用户是否加入了某个图书馆，以及加入状态",
            description = "## 功能描述\n" +
                    "根据图书馆ID查询当前用户是否加入了该图书馆，并返回其加入状态。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/join-status\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 要查询的图书馆ID | 123 |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,                     // 响应码：0-成功，非0-失败\n" +
                    "  \"message\": \"查询成功\",         // 响应消息\n" +
                    "  \"result\": {\n" +
                    "    \"joined\": true,              // 是否已加入该图书馆\n" +
                    "    \"status\": 2,                 // 加入状态：0-拒绝，1-已邀请待确认，2-已确认加入，-1-未找到记录\n" +
                    "    \"statusDescription\": \"已确认加入\"  // 状态描述\n" +
                    "  }\n" +
                    "}\n" +
                    "```\n\n" +
                    "## 响应状态码说明\n" +
                    "| 状态码 | 描述 | 可能原因 |\n" +
                    "|--------|------|----------|\n" +
                    "| 200 | 成功 | 请求成功处理 |\n" +
                    "| 400 | 请求参数错误 | 缺少必要参数或参数格式错误 |\n" +
                    "| 401 | 未授权 | 用户未登录或token无效 |\n" +
                    "| 404 | 资源不存在 | 指定的图书馆ID不存在 |\n" +
                    "| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功查询加入状态",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<RestLibraryJoinStatus> getLibraryJoinStatus(
            @Parameter(description = "请求参数，包含图书馆ID", required = true)
            @RequestBody RestLibraryRequest libraryRequest,
            HttpServletRequest request) {

        libraryRequest.initHeaderValue(request);

        ExecuteResult<RestLibraryJoinStatus> result = new ExecuteResult<>();
        RestLibraryJoinStatus joinStatus = new RestLibraryJoinStatus();
        joinStatus.setJoined(false); // 默认未加入
        joinStatus.setStatus(-1); // 默认状态码
        joinStatus.setStatusDescription(RestLibraryJoinStatus.getStatusDescription(-1));

        // 参数验证
        if (libraryRequest.getLibraryId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_ID_REQUIRED);
            return result;
        }

        // 获取当前用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 调用Service层查询用户图书馆加入状态
        ExecuteResult<LibUser> serviceResult = libUserService.queryUserLibraryJoinStatus(userEmail, libraryRequest.getLibraryId());

        if (serviceResult.isSuccess()) {
            LibUser libUser = serviceResult.getResult();
            if (libUser != null) {
                joinStatus.setJoined(true);
                joinStatus.setStatus(libUser.getStatus());
                joinStatus.setStatusDescription(RestLibraryJoinStatus.getStatusDescription(libUser.getStatus()));
            }
            result.setResult(joinStatus);
            result.setSuccessMessage(ReturnInfo.SUCCESS_QUERY);
        } else {
            result.setErrorMessages(serviceResult.getErrorMessages());
        }

        return result;
    }

    /**
     * 获取用户借阅记录列表
     *
     * @param queryRequest 查询请求参数
     * @param request      HTTP请求
     * @return 用户借阅记录分页列表
     */
    @RequestMapping(value = "/me/loans", method = RequestMethod.POST)
    @Operation(summary = "获取用户借阅记录列表",
            description = "## 功能描述\n" +
                    "获取当前用户的借阅记录列表，包含书籍和图书馆详细信息。支持按状态、图书馆、书名关键词过滤，以及排序功能。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/me/loans\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| page | Integer | 是 | 页码，从1开始 | 1 |\n" +
                    "| status | Integer | 否 | 借阅状态：0-借阅中，1-已归还，2-已逾期 | 0 |\n" +
                    "| libraryId | Long | 否 | 图书馆ID | 123 |\n" +
                    "| bookTitle | String | 否 | 书名关键词 | \"Java\" |\n" +
                    "| sortBy | String | 否 | 排序字段：borrow_at, due_at, return_at | \"borrow_at\" |\n" +
                    "| sortDirection | String | 否 | 排序方向：desc-降序，asc-升序 | \"desc\" |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,                     // 响应码：0-成功，非0-失败\n" +
                    "  \"message\": \"查询成功\",         // 响应消息\n" +
                    "  \"result\": [                    // 借阅记录列表\n" +
                    "    {\n" +
                    "      \"libBorrowId\": 1001,      // 借阅记录ID\n" +
                    "      \"userEmail\": \"<EMAIL>\",  // 用户邮箱\n" +
                    "      \"libraryId\": 123,         // 图书馆ID\n" +
                    "      \"libraryName\": \"中央图书馆\",  // 图书馆名称\n" +
                    "      \"bookId\": 456,            // 书籍ID\n" +
                    "      \"bookTitle\": \"深入理解Java虚拟机\",  // 书籍标题\n" +
                    "      \"bookAuthor\": \"周志明\",   // 书籍作者\n" +
                    "      \"bookCoverUrl\": \"https://example.com/covers/9787111421900.jpg\",  // 书籍封面URL\n" +
                    "      \"purchaseId\": 789,        // 关联的购买记录ID\n" +
                    "      \"status\": 0,              // 借阅状态：0-借阅中，1-已归还，2-已逾期\n" +
                    "      \"statusDescription\": \"借阅中\",  // 状态描述\n" +
                    "      \"borrowAt\": \"2025-07-15 14:30:22\",  // 借阅时间\n" +
                    "      \"dueAt\": \"2025-08-14 23:59:59\",     // 应归还时间\n" +
                    "      \"returnAt\": null,         // 实际归还时间（未归还时为null）\n" +
                    "      \"createdAt\": \"2025-07-15 14:30:22\", // 创建时间\n" +
                    "      \"isOverdue\": false,       // 是否逾期\n" +
                    "      \"remainingDays\": 15       // 剩余天数（负数表示逾期天数）\n" +
                    "    }\n" +
                    "  ],\n" +
                    "  \"currentPage\": 1,             // 当前页码\n" +
                    "  \"totalRecord\": 25,            // 总记录数\n" +
                    "  \"pageSize\": 10                // 每页记录数\n" +
                    "}\n" +
                    "```\n\n" +
                    "## 响应状态码说明\n" +
                    "| 状态码 | 描述 | 可能原因 |\n" +
                    "|--------|------|----------|\n" +
                    "| 200 | 成功 | 请求成功处理 |\n" +
                    "| 400 | 请求参数错误 | 缺少必要参数或参数格式错误 |\n" +
                    "| 401 | 未授权 | 用户未登录或token无效 |\n" +
                    "| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取借阅记录列表",
                    content = @Content(schema = @Schema(implementation = PageResult.class)))
    })
    public PageResult<RestLibBorrow> getUserBorrowRecords(
            @Parameter(description = "查询请求参数", required = true)
            @RequestBody RestLibBorrowQuery queryRequest,
            HttpServletRequest request) {
        queryRequest.initHeaderValue(request);

        PageResult<RestLibBorrow> pageResult = new PageResult<>();
        if (queryRequest.getPage() == null) {
            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }

        // 获取当前用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            pageResult.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return pageResult;
        }

        Page<LibBorrowWithDetail> page = new Page<>();
        page.setCurrentPage(queryRequest.getPage());
        page.setPageSize(pageResult.getPageSize());

        // 查询用户借阅记录（带详细信息）
        page = libBorrowService.queryUserBorrowRecordsWithDetail(
                page,
                userEmail,
                queryRequest.getStatus(),
                queryRequest.getLibraryId(),
                queryRequest.getBookTitle(),
                queryRequest.getSortBy(),
                queryRequest.getSortDirection()
        );

        // 转换为前端模型
        for (LibBorrowWithDetail borrowDetail : page.getRecords()) {
            RestLibBorrow restBorrow = new RestLibBorrow();
            restBorrow.initFrom(borrowDetail);
            pageResult.getResult().add(restBorrow);
        }

        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());

        return pageResult;
    }

    /**
     * 获取图书馆中特定书籍的详情
     *
     * @param bookRequest 请求参数，包含图书馆ID和书籍ID
     * @param request     HTTP请求，用于获取用户身份信息
     * @return 返回包含书籍详情的执行结果
     */
    @RequestMapping(value = "/book-detail", method = RequestMethod.POST)
    @Operation(summary = "获取图书馆中特定书籍的详情",
            description = "## 功能描述\n" +
                    "根据图书馆ID和书籍ID获取特定书籍的详细信息，包括书籍基本信息、库存状态、用户借阅状态等。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/book-detail\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 123 |\n" +
                    "| libBookId | Long | 是 | 图书馆藏书ID | 456 |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,                     // 响应码：0-成功，非0-失败\n" +
                    "  \"message\": \"查询成功\",         // 响应消息\n" +
                    "  \"result\": {                    // 书籍详情\n" +
                    "    \"libBookId\": 789,           // 图书馆书籍ID\n" +
                    "    \"libraryId\": 123,           // 图书馆ID\n" +
                    "    \"bookId\": 456,             // 书籍ID\n" +
                    "    \"bookTitle\": \"深入理解Java虚拟机\",  // 书籍标题\n" +
                    "    \"bookAuthor\": \"周志明\",    // 作者\n" +
                    "    \"bookDescription\": \"深入理解Java虚拟机的原理与实现...\",  // 书籍描述\n" +
                    "    \"bookCoverUrl\": \"https://example.com/covers/9787111421900.jpg\",  // 封面URL\n" +
                    "    \"bookPublisher\": \"机械工业出版社\",  // 出版社\n" +
                    "    \"bookIsbn\": \"9787111421900\",  // ISBN号\n" +
                    "    \"bookPrice\": 99.00,         // 书籍价格\n" +
                    "    \"totalCopies\": 10,          // 总藏书量\n" +
                    "    \"availableCopies\": 3,       // 当前可借数量\n" +
                    "    \"loanDays\": 30,             // 可借阅天数\n" +
                    "    \"isUserBorrowed\": true,     // 用户是否已借阅\n" +
                    "    \"isUserPurchased\": false,   // 用户是否已购买\n" +
                    "    \"isUserReserved\": false,    // 用户是否已预约\n" +
                    "    \"libBorrowId\": 1001,        // 借阅记录ID（如已借阅）\n" +
                    "    \"borrowAt\": \"2025-07-15 14:30:22\",  // 借阅时间\n" +
                    "    \"dueAt\": \"2025-08-14 23:59:59\",     // 应归还时间\n" +
                    "    \"borrowStatus\": 0,          // 借阅状态：0-借阅中，1-已归还，2-已逾期\n" +
                    "    \"libReservationId\": null,   // 预约记录ID（如已预约）\n" +
                    "    \"reservationAt\": null,     // 预约时间\n" +
                    "    \"queuePosition\": 3,         // 用户在预约队列中的位置（null表示未预约）\n" +
                    "    \"totalWaitingCount\": 15     // 预约队列的总等待人数\n" +
                    "  }\n" +
                    "}\n" +
                    "```\n\n" +
                    "## 响应状态码说明\n" +
                    "| 状态码 | 描述 | 可能原因 |\n" +
                    "|--------|------|----------|\n" +
                    "| 200 | 成功 | 请求成功处理 |\n" +
                    "| 400 | 请求参数错误 | 缺少必要参数或参数格式错误 |\n" +
                    "| 401 | 未授权 | 用户未登录或token无效 |\n" +
                    "| 403 | 禁止访问 | 用户无权限访问该图书馆 |\n" +
                    "| 404 | 资源不存在 | 指定的图书馆或书籍ID不存在 |\n" +
                    "| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |"
    )

    public ExecuteResult<RestLibBookDetail> getLibraryBookDetail(
            @Parameter(description = "请求参数，包含图书馆ID和书籍ID", required = true)
            @RequestBody RestLibBook bookRequest,
            HttpServletRequest request) {

        bookRequest.initHeaderValue(request);
        
        ExecuteResult<RestLibBookDetail> result = new ExecuteResult<>();

        // 参数验证
        if (bookRequest.getLibraryId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_ID_REQUIRED);
            return result;
        }
        if (bookRequest.getLibBookId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_BOOK_NOT_EXIST);
            return result;
        }

        // 获取当前用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 调用服务层查询书籍详情
        ExecuteResult<LibBookDetailWithUserStatus> serviceResult =
            libBookService.getLibBookDetailWithUserStatus(bookRequest.getLibraryId(), bookRequest.getLibBookId(), userEmail);

        if (serviceResult.isSuccess()) {
            LibBookDetailWithUserStatus detail = serviceResult.getResult();

            // 转换为前端模型
            RestLibBookDetail restDetail = new RestLibBookDetail();
            restDetail.initFrom(detail);
            
            // 获取预约队列信息
            ExecuteResult<LibReservationService.QueueInfo> queueInfoResult = 
                    libReservationService.getReservationQueueInfo(bookRequest.getLibraryId(), bookRequest.getLibBookId(), userEmail);
            
            if (queueInfoResult.isSuccess()) {
                LibReservationService.QueueInfo queueInfo = queueInfoResult.getResult();
                restDetail.setQueuePosition(queueInfo.getQueuePosition());
                restDetail.setTotalWaitingCount(queueInfo.getTotalWaitingCount());
            } else {
                // 如果获取队列信息失败，设置为默认值
                restDetail.setQueuePosition(null);
                restDetail.setTotalWaitingCount(0);
                log.warn("获取预约队列信息失败: {}", queueInfoResult.getErrorMessages());
            }

            result.setResult(restDetail);
            result.setSuccessMessage(ReturnInfo.SUCCESS_QUERY);
        } else {
            result.setErrorMessages(serviceResult.getErrorMessages());
        }

        return result;
    }

    /**
     * 用户预约图书
     *
     * @param reservationRequest 预约请求
     * @param request            HTTP请求
     * @return 预约结果
     */
    @RequestMapping(value = "/books/reserve", method = RequestMethod.POST)
    @Operation(summary = "用户预约图书",
            description = "## 功能描述\n" +
                    "用户预约已借出的图书。系统会检查用户预约权限、图书可预约状态，并处理预约逻辑。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/books/reserve\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 1 |\n" +
                    "| bookId | Long | 是 | 书籍ID | 1001 |\n\n" +
                    "## 业务逻辑\n" +
                    "1. 验证用户是否已加入图书馆\n" +
                    "2. 检查用户是否已借阅或预约同一本书\n" +
                    "3. 检查用户预约数量是否超过限制（与借阅数量共享10本限制）\n" +
                    "4. 检查书籍是否已无库存\n" +
                    "5. 计算并返回预计可借时间\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"预约成功\",\n" +
                    "  \"result\": \"预约成功，预约记录ID: 7001，预计2025-08-15可借阅\"\n" +
                    "}\n" +
                    "```")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "预约操作完成",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<String> reserveBook(
            @Parameter(description = "预约请求参数", required = true)
            @RequestBody RestReservationRequest reservationRequest,
            HttpServletRequest request) {
        reservationRequest.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();

        // 参数验证
        if (reservationRequest.getLibraryId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_ID_REQUIRED);
            return result;
        }
        if (reservationRequest.getBookId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_BOOK_NOT_EXIST);
            return result;
        }

        // 获取用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 执行预约操作
        ExecuteResult<LibReservation> reservationResult = libReservationService.reserveBook(
                reservationRequest.getLibraryId(),
                reservationRequest.getBookId(),
                userEmail
        );

        if (reservationResult.isSuccess()) {
            LibReservation reservation = reservationResult.getResult();
            String message = String.format("预约成功，预约记录ID: %d", reservation.getLibReservationId());
            if (reservation.getEstimatedAvailableAt() != null) {
                message += String.format("，预计%tF可借阅", reservation.getEstimatedAvailableAt());
            }
            result.setSuccessMessage(ReturnInfo.SUCCESS_BOOK_RESERVED);
            result.setResult(message);
        } else {
            result.setErrorMessages(reservationResult.getErrorMessages());
        }

        return result;
    }

    /**
     * 用户取消预约
     *
     * @param cancelRequest 取消预约请求
     * @param request       HTTP请求
     * @return 取消结果
     */
    @RequestMapping(value = "/reservations/cancel", method = RequestMethod.POST)
    @Operation(summary = "用户取消预约",
            description = "## 功能描述\n" +
                    "用户取消图书预约。系统会验证预约记录权限，并更新后续预约用户的预计可借时间。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/reservations/cancel\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| reservationId | Long | 是 | 预约记录ID | 7001 |\n\n" +
                    "## 业务逻辑\n" +
                    "1. 验证预约记录是否存在且属于当前用户\n" +
                    "2. 检查预约状态是否允许取消\n" +
                    "3. 更新预约状态为\"已取消\"\n" +
                    "4. 如果该预约是队列中的非尾部，更新后续预约用户的预计可借时间\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"取消预约成功\",\n" +
                    "  \"result\": \"预约已取消\"\n" +
                    "}\n" +
                    "```")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "取消预约操作完成",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<String> cancelReservation(
            @Parameter(description = "取消预约请求参数", required = true)
            @RequestBody RestReservationRequest cancelRequest,
            HttpServletRequest request) {
        cancelRequest.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();

        // 参数验证
        if (cancelRequest.getReservationId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_RESERVATION_NOT_EXIST);
            return result;
        }

        // 获取用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 执行取消预约操作
        ExecuteResult<Boolean> cancelResult = libReservationService.cancelReservation(
                cancelRequest.getReservationId(),
                userEmail
        );

        if (cancelResult.isSuccess()) {
            result.setSuccessMessage(ReturnInfo.SUCCESS_RESERVATION_CANCELLED);
            result.setResult("预约已取消");
        } else {
            result.setErrorMessages(cancelResult.getErrorMessages());
        }

        return result;
    }

    /**
     * 获取当前用户的预约记录列表
     *
     * @param queryRequest 查询请求参数
     * @param request      HTTP请求
     * @return 用户预约记录分页列表
     */
    @RequestMapping(value = "/me/reservations", method = RequestMethod.POST)
    @Operation(summary = "获取当前用户的预约记录列表",
            description = "## 功能描述\n" +
                    "获取当前用户的预约记录列表，包含书籍和图书馆详细信息。支持按状态、图书馆、书名关键词过滤。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/me/reservations\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| page | Integer | 是 | 当前页码，从1开始 | 1 |\n" +
                    "| status | Integer | 否 | 预约状态：0-等待中，1-已借阅，2-已取消 | 0 |\n" +
                    "| libraryId | Long | 否 | 图书馆ID | 1 |\n" +
                    "| bookTitle | String | 否 | 书籍标题关键词 | Java |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"查询成功\",\n" +
                    "  \"result\": [\n" +
                    "    {\n" +
                    "      \"libReservationId\": 7001,\n" +
                    "      \"libraryName\": \"中央图书馆\",\n" +
                    "      \"bookTitle\": \"Java编程思想\",\n" +
                    "      \"bookAuthor\": \"Bruce Eckel\",\n" +
                    "      \"status\": 0,\n" +
                    "      \"statusDescription\": \"等待中\",\n" +
                    "      \"reservationAt\": \"2025-07-31 14:30:22\",\n" +
                    "      \"estimatedAvailableAt\": \"2025-08-15 14:30:22\",\n" +
                    "      \"queuePosition\": 3,\n" +
                    "      \"totalWaitingCount\": 15\n" +
                    "    }\n" +
                    "  ],\n" +
                    "  \"pageSize\": 10,\n" +
                    "  \"currentPage\": 1,\n" +
                    "  \"totalRecord\": 5\n" +
                    "}\n" +
                    "```")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取预约记录列表",
                    content = @Content(schema = @Schema(implementation = PageResult.class)))
    })
    public PageResult<RestReservation> getUserReservations(
            @Parameter(description = "查询请求参数", required = true)
            @RequestBody RestReservationQuery queryRequest,
            HttpServletRequest request) {
        queryRequest.initHeaderValue(request);

        PageResult<RestReservation> pageResult = new PageResult<>();
        if (queryRequest.getPage() == null) {
            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }

        // 获取当前用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            pageResult.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return pageResult;
        }

        Page<LibReservationWithDetail> page = new Page<>();
        page.setCurrentPage(queryRequest.getPage());
        page.setPageSize(pageResult.getPageSize());

        // 查询用户预约记录（带详细信息）
        page = libReservationService.queryUserReservationsWithDetail(
                page,
                userEmail,
                queryRequest.getStatus(),
                queryRequest.getLibraryId()
        );

        // 转换为前端模型
        for (LibReservationWithDetail reservationDetail : page.getRecords()) {
            RestReservation restReservation = new RestReservation();
            restReservation.initFrom(reservationDetail);
            pageResult.getResult().add(restReservation);
        }

        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());

        return pageResult;
    }

    /**
     * 获取用户在图书馆的所有分组
     *
     * @param groupRequest 分组请求参数
     * @param request      HTTP请求
     * @return 分组名称列表
     */
    @RequestMapping(value = "/groups", method = RequestMethod.POST)
    @Operation(summary = "获取用户在图书馆的所有分组",
            description = "## 功能描述\n" +
                    "获取当前用户在指定图书馆的所有分组名称列表。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/groups\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 123 |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"查询成功\",\n" +
                    "  \"result\": [\"技术类\", \"文学类\", \"生活类\"]\n" +
                    "}\n" +
                    "```")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取分组列表",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<java.util.List<String>> getUserLibraryGroups(
            @Parameter(description = "分组请求参数", required = true)
            @RequestBody RestLibraryRequest groupRequest,
            HttpServletRequest request) {
        groupRequest.initHeaderValue(request);

        ExecuteResult<java.util.List<String>> result = new ExecuteResult<>();

        // 参数验证
        if (groupRequest.getLibraryId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_ID_REQUIRED);
            return result;
        }

        // 获取用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 获取用户分组
        java.util.List<String> groups = libBorrowService.getUserGroupNames(userEmail, groupRequest.getLibraryId());
        result.setResult(groups);
        result.setSuccessMessage(ReturnInfo.SUCCESS_QUERY);

        return result;
    }

    /**
     * 更新图书馆书籍分组
     *
     * @param updateRequest 更新分组请求参数
     * @param request       HTTP请求
     * @return 更新结果
     */
    @RequestMapping(value = "/books/update-group", method = RequestMethod.POST)
    @Operation(summary = "更新图书馆书籍分组",
            description = "## 功能描述\n" +
                    "批量更新用户在指定图书馆的书籍分组信息。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/books/update-group\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 123 |\n" +
                    "| libBookIds | List<Long> | 是 | 馆藏图书ID列表（从用户借阅记录获取） | [1, 2, 3] |\n" +
                    "| groupName | String | 否 | 分组名称，空字符串表示移除分组 | \"技术类\" |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"更新成功\",\n" +
                    "  \"result\": \"更新成功，影响3条记录\"\n" +
                    "}\n" +
                    "```")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分组更新完成",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<String> updateBooksGroup(
            @Parameter(description = "更新分组请求参数", required = true)
            @RequestBody RestLibBookGroupRequest updateRequest,
            HttpServletRequest request) {
        updateRequest.initHeaderValue(request);

        ExecuteResult<String> result = new ExecuteResult<>();

        // 参数验证
        if (updateRequest.getLibraryId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_ID_REQUIRED);
            return result;
        }
        if (updateRequest.getLibBookIds() == null || updateRequest.getLibBookIds().isEmpty()) {
            result.addErrorMessage(ReturnInfo.ERROR_LIB_BOOK_IDS_REQUIRED);
            return result;
        }

        // 获取用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 1. 用户图书馆权限校验
        ExecuteResult<LibUser> userStatusResult = libUserService.queryUserLibraryJoinStatus(userEmail, updateRequest.getLibraryId());
        if (!userStatusResult.isSuccess() || userStatusResult.getResult() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_USER_NOT_IN_LIBRARY);
            return result;
        }
        
        LibUser libUser = userStatusResult.getResult();
        if (libUser.getStatus() == null || libUser.getStatus() != 2) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_USER_STATUS_INVALID);
            return result;
        }

        // 2. 验证书籍归属权
        ExecuteResult<List<LibBook>> ownershipResult = libBookService.validateBooksOwnership(
                updateRequest.getLibBookIds(), updateRequest.getLibraryId());
        if (!ownershipResult.isSuccess()) {
            result.setErrorMessages(ownershipResult.getErrorMessages());
            return result;
        }

        // 3. 批量验证借阅记录状态
        List<LibBorrow> activeBorrows = libBorrowService.validateActiveBorrowRecords(
                userEmail, updateRequest.getLibraryId(), updateRequest.getLibBookIds());
        if (activeBorrows.size() != updateRequest.getLibBookIds().size()) {
            List<Long> validLibBookIds = activeBorrows.stream()
                    .map(LibBorrow::getLibBookId)
                    .collect(java.util.stream.Collectors.toList());
            
            List<Long> invalidIds = updateRequest.getLibBookIds().stream()
                    .filter(id -> !validLibBookIds.contains(id))
                    .collect(java.util.stream.Collectors.toList());
                    
            result.addErrorMessage("以下书籍不在正常借阅状态，无法更新分组: " + invalidIds);
            return result;
        }

        // 创建LibBorrow对象
        LibBorrow libBorrow = new LibBorrow();
        libBorrow.setUserEmail(userEmail);
        libBorrow.setLibraryId(updateRequest.getLibraryId());
        libBorrow.setGroupName(updateRequest.getGroupName() == null ? "" : updateRequest.getGroupName());
        libBorrow.setGroupNameTime(System.currentTimeMillis());

        // 执行批量更新
        int updatedCount = libBorrowService.updateGroupNameByEmailAndLibBookIds(libBorrow, updateRequest.getLibBookIds());
        
        if (updatedCount > 0) {
            result.setSuccessMessage(ReturnInfo.SUCCESS_GROUP_UPDATED);
            result.setResult("更新成功，影响" + updatedCount + "条记录");
        } else {
            // 理论上不应该走到这里，因为前面已经验证过借阅记录状态
            result.addErrorMessage("更新失败：没有符合条件的借阅记录被更新");
            log.warn("批量更新分组失败，updatedCount=0, userEmail={}, libraryId={}, libBookIds={}", 
                    userEmail, updateRequest.getLibraryId(), updateRequest.getLibBookIds());
        }

        return result;
    }

    /**
     * 获取用户在特定图书馆特定分组的所有书籍
     *
     * @param groupBooksRequest 分组书籍查询请求
     * @param request           HTTP请求
     * @return 书籍列表
     */
    @RequestMapping(value = "/group-books", method = RequestMethod.POST)
    @Operation(summary = "获取用户在特定图书馆特定分组的所有书籍",
            description = "## 功能描述\n" +
                    "获取当前用户在指定图书馆指定分组的所有书籍列表，包含书籍详细信息。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/group-books\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 123 |\n" +
                    "| groupName | String | 是 | 分组名称 | \"技术类\" |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"查询成功\",\n" +
                    "  \"result\": [\n" +
                    "    {\n" +
                    "      \"libBorrowId\": 1001,\n" +
                    "      \"userEmail\": \"<EMAIL>\",\n" +
                    "      \"libraryId\": 123,\n" +
                    "      \"libraryName\": \"中央图书馆\",\n" +
                    "      \"bookId\": 456,\n" +
                    "      \"bookTitle\": \"Java编程思想\",\n" +
                    "      \"bookAuthor\": \"Bruce Eckel\",\n" +
                    "      \"bookDescription\": \"深入理解Java编程的经典教材\",\n" +
                    "      \"bookCoverUrl\": \"https://example.com/covers/java.jpg\",\n" +
                    "      \"bookPublisher\": \"机械工业出版社\",\n" +
                    "      \"bookPages\": 880,\n" +
                    "      \"status\": 0,\n" +
                    "      \"statusDescription\": \"借阅中\",\n" +
                    "      \"borrowAt\": \"2025-07-15 14:30:22\",\n" +
                    "      \"dueAt\": \"2025-08-14 23:59:59\",\n" +
                    "      \"groupName\": \"技术类\",\n" +
                    "      \"groupNameTime\": 1690262400000,\n" +
                    "      \"readInfo\": \"{\\\"cfi\\\":\\\"epubcfi(...)\\\"}\",\n" +
                    "      \"readTime\": 1690262450000\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}\n" +
                    "```")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取分组书籍列表",
                    content = @Content(schema = @Schema(implementation = ExecuteResult.class)))
    })
    public ExecuteResult<java.util.List<RestLibBorrow>> getGroupBooks(
            @Parameter(description = "分组书籍查询请求", required = true)
            @RequestBody RestLibBookGroupRequest groupBooksRequest,
            HttpServletRequest request) {
        groupBooksRequest.initHeaderValue(request);

        ExecuteResult<java.util.List<RestLibBorrow>> result = new ExecuteResult<>();

        // 参数验证
        if (groupBooksRequest.getLibraryId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_LIBRARY_ID_REQUIRED);
            return result;
        }
        if (StringUtils.isBlank(groupBooksRequest.getGroupName())) {
            result.addErrorMessage(ReturnInfo.ERROR_GROUP_NAME_REQUIRED);
            return result;
        }

        // 获取用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            result.addErrorMessage(ReturnInfo.ERROR_EMAIL_NOT_EXIST);
            return result;
        }

        // 获取分组书籍详细信息
        java.util.List<LibBorrowWithDetail> borrowsWithDetail = libBorrowService.getBooksWithDetailByUserEmailAndGroupName(
                userEmail, groupBooksRequest.getLibraryId(), groupBooksRequest.getGroupName());
        
        // 转换为前端模型
        java.util.List<RestLibBorrow> restBorrows = new java.util.ArrayList<>();
        for (LibBorrowWithDetail borrowDetail : borrowsWithDetail) {
            RestLibBorrow restBorrow = new RestLibBorrow();
            restBorrow.initFrom(borrowDetail);
            restBorrows.add(restBorrow);
        }
        
        result.setResult(restBorrows);
        result.setSuccessMessage(ReturnInfo.SUCCESS_QUERY);

        return result;
    }
    
    /**
     * 下载已借阅的书籍文件
     *
     * @param downloadRequest 下载请求参数
     * @param request         HTTP请求
     * @return 书籍文件字节流
     */
    @RequestMapping(value = "/books/download", method = RequestMethod.POST)
    @Operation(summary = "下载已借阅的书籍",
            description = "## 功能描述\n" +
                    "下载用户已借阅的书籍LCP文件。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/books/download\n\n" +
                    "## 请求头\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| Authorization | String | 是 | 用户认证token | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |\n" +
                    "| Content-Type | String | 是 | 请求体类型 | application/json |\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| borrowId | Long | 是 | 借阅记录ID | 1001 |\n\n" +
                    "## 响应说明\n" +
                    "- **成功响应**：返回书籍文件的字节流，Content-Type为application/epub+zip\n" +
                    "- **失败响应**：返回错误信息\n\n" +
                    "## 响应示例\n" +
                    "### 成功响应\n" +
                    "返回书籍文件的二进制数据流\n\n" +
                    "### 失败响应\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 400,\n" +
                    "  \"message\": \"借阅记录不存在\"\n" +
                    "}\n" +
                    "```")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "下载成功",
                    content = @Content(mediaType = "application/epub+zip")),
            @ApiResponse(responseCode = "500", description = "参数错误或下载失败")
    })
    public ResponseEntity<byte[]> downloadBook(
            @Parameter(description = "下载请求参数", required = true)
            @RequestBody RestLibBookDownloadRequest downloadRequest,
            HttpServletRequest request) {
        
        // 参数验证
        if (downloadRequest.getBorrowId() == null) {
            throw new DownloadException(Integer.valueOf(201), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "借阅记录ID不能为空，downloadRequest=" + downloadRequest);
        }
        
        // 获取用户邮箱
        String userEmail = getRestEmail(request);
        if (StringUtils.isBlank(userEmail)) {
            throw new DownloadException(Integer.valueOf(202), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, "用户邮箱不存在，downloadRequest=" + downloadRequest);
        }

        // 调用下载服务
        ExecuteResult<byte[]> downloadResult = libBorrowService.downloadBorrowedBook(downloadRequest.getBorrowId(), userEmail);
        
        if (!downloadResult.isSuccess() || downloadResult.getResult() == null) {
            String errorMsg = String.join(", ", downloadResult.getErrorMessages());
            if (StringUtils.isBlank(errorMsg)) {
                errorMsg = "下载失败";
            }
            throw new DownloadException(Integer.valueOf(203), InterfaceContant.ApiErrorConfig.DOWNLOAD_ERROR_CODE, errorMsg + "，downloadRequest=" + downloadRequest);
        }

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/epub+zip"));
        headers.setContentDispositionFormData("attachment", "book_" + downloadRequest.getBorrowId() + ".epub");
        headers.setContentLength(downloadResult.getResult().length);

        return ResponseEntity.ok()
                .headers(headers)
                .body(downloadResult.getResult());
    }


    /**
     * 获取图书馆所有书单及其书籍列表
     *
     * @param request 请求参数，包含图书馆ID和可选的书籍数量限制
     * @param httpRequest HTTP请求对象
     * @return 书单列表及其书籍信息
     */
    @RequestMapping(value = "/booklists", method = RequestMethod.POST)
    @Operation(summary = "获取图书馆所有书单列表",
            description = "## 功能描述\n" +
                    "获取指定图书馆的所有有效且显示的书单列表，每个书单包含其基本信息和书籍详情。\n" +
                    "支持限制每个书单返回的书籍数量，避免响应过大。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/booklists\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 1 |\n" +
                    "| booksLimit | Integer | 否 | 每个书单返回的书籍数量限制，不传则全部返回 | 10 |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"查询成功\",\n" +
                    "  \"result\": {\n" +
                    "    \"lists\": [\n" +
                    "      {\n" +
                    "        \"id\": 1001,\n" +
                    "        \"name\": \"新书速递\",\n" +
                    "        \"nameTc\": \"新書速遞\",\n" +
                    "        \"nameEn\": \"New Arrivals\",\n" +
                    "        \"bannerUrl\": \"https://example.com/banner.jpg\",\n" +
                    "        \"listAbstract\": \"最新上架图书推荐\",\n" +
                    "        \"showOrder\": 10,\n" +
                    "        \"bookCount\": 3,\n" +
                    "        \"books\": [\n" +
                    "          {\n" +
                    "            \"bookId\": 101,\n" +
                    "            \"name\": \"图书名称\",\n" +
                    "            \"author\": \"作者姓名\",\n" +
                    "            \"imgUrl\": \"https://example.com/cover.jpg\",\n" +
                    "            \"isbn\": \"9787111234567\",\n" +
                    "            \"publisherName\": \"出版社\",\n" +
                    "            \"bookDescription\": \"图书简介\",\n" +
                    "            \"categoryId\": 5\n" +
                    "          }\n" +
                    "        ]\n" +
                    "      }\n" +
                    "    ]\n" +
                    "  }\n" +
                    "}\n" +
                    "```\n\n" +
                    "## 注意事项\n" +
                    "- 只返回 is_valid=1 且 is_show=1 的书单\n" +
                    "- 书单按 show_order DESC 排序，显示顺序值越大推荐度越高\n" +
                    "- 书单内书籍按 book_id_text 中的顺序返回\n" +
                    "- 书单内的书籍已过滤为本图书馆馆藏")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ExecuteResult.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "系统内部错误")
    })
    public ExecuteResult<List<RestLibraryBookList>> getLibraryBookLists(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "请求参数",
                    required = true,
                    content = @Content(schema = @Schema(implementation = RestLibraryBookListRequest.class))
            )
            @RequestBody RestLibraryBookListRequest request,
            HttpServletRequest httpRequest) {

        ExecuteResult<List<RestLibraryBookList>> result = new ExecuteResult<>();

        try {
            // 参数验证
            if (request.getLibraryId() == null) {
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                result.addErrorMessage("libraryId 不能为空");
                return result;
            }

            log.info("获取图书馆书单列表: libraryId={}, booksLimit={}", 
                     request.getLibraryId(), request.getBooksLimit());

            // 获取图书馆所有有效书单
            List<LibBookList> bookLists = libBookListService.getValidBookListsByLibraryId(request.getLibraryId());

            // 构建响应数据
            List<RestLibraryBookList> bookListInfos = new ArrayList<>();
            for (LibBookList bookList : bookLists) {
                RestLibraryBookList listInfo = new RestLibraryBookList();
                listInfo.setId(bookList.getLibListId());
                listInfo.setName(bookList.getListName());
                listInfo.setNameTc(bookList.getListNameTc());
                listInfo.setNameEn(bookList.getListNameEn());
                listInfo.setBannerUrl(bookList.getBannerUrl());
                listInfo.setListAbstract(bookList.getListAbstract());
                listInfo.setShowOrder(bookList.getShowOrder());

                // 获取书单中的书籍详情
                List<RestBook> books = libBookListService.parseAndGetRestBooks(
                        bookList.getBookIdText(), 
                        request.getLibraryId(), 
                        request.getBooksLimit()
                );
                
                listInfo.setBooks(books);
                listInfo.setBookCount(books.size());
                
                bookListInfos.add(listInfo);
            }

            result.setResult(bookListInfos);
            result.setSuccessMessage(ReturnInfo.SUCCESS_QUERY);

        } catch (Exception e) {
            log.error("获取图书馆书单列表失败: libraryId={}", request.getLibraryId(), e);
            result.addErrorMessage("系统内部错误");
            result.addErrorMessage("获取书单列表失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取单个图书馆书单及其书籍详情
     *
     * @param request 请求参数，包含图书馆ID、书单ID和可选的书籍数量限制
     * @param httpRequest HTTP请求对象
     * @return 书单信息及其书籍详情
     */
    @RequestMapping(value = "/booklists/get", method = RequestMethod.POST)
    @Operation(summary = "获取单个图书馆书单详情",
            description = "## 功能描述\n" +
                    "获取指定图书馆的单个书单详情，包含书单基本信息和书籍列表。\n" +
                    "支持限制返回的书籍数量，适用于分页或分屏加载场景。\n\n" +
                    "## 请求方式\n" +
                    "- **请求方法**：POST\n" +
                    "- **请求路径**：/api/v4/library/booklists/get\n\n" +
                    "## 请求体参数\n" +
                    "| 参数名 | 类型 | 是否必须 | 描述 | 示例 |\n" +
                    "|--------|------|------|------|------|\n" +
                    "| libraryId | Long | 是 | 图书馆ID | 1 |\n" +
                    "| listId | Long | 是 | 书单ID | 1001 |\n" +
                    "| booksLimit | Integer | 否 | 返回的书籍数量限制，不传则全部返回 | 20 |\n\n" +
                    "## 响应示例\n" +
                    "```json\n" +
                    "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"查询成功\",\n" +
                    "  \"result\": {\n" +
                    "    \"id\": 1001,\n" +
                    "    \"name\": \"经典文学\",\n" +
                    "    \"nameTc\": \"經典文學\",\n" +
                    "    \"nameEn\": \"Classic Literature\",\n" +
                    "    \"bannerUrl\": \"https://example.com/banner.jpg\",\n" +
                    "    \"listAbstract\": \"精选世界经典文学作品\",\n" +
                    "    \"showOrder\": 10,\n" +
                    "    \"bookCount\": 15,\n" +
                    "    \"books\": [\n" +
                    "      {\n" +
                    "        \"bookId\": 201,\n" +
                    "        \"name\": \"红楼梦\",\n" +
                    "        \"author\": \"曹雪芹\",\n" +
                    "        \"imgUrl\": \"https://example.com/cover.jpg\",\n" +
                    "        \"isbn\": \"9787111234567\"\n" +
                    "      }\n" +
                    "    ]\n" +
                    "  }\n" +
                    "}\n" +
                    "```")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ExecuteResult.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "404", description = "书单不存在"),
            @ApiResponse(responseCode = "500", description = "系统内部错误")
    })
    public ExecuteResult<RestLibraryBookList> getLibraryBookList(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "请求参数",
                    required = true,
                    content = @Content(schema = @Schema(implementation = RestLibraryBookListRequest.class))
            )
            @RequestBody RestLibraryBookListRequest request,
            HttpServletRequest httpRequest) {

        ExecuteResult<RestLibraryBookList> result = new ExecuteResult<>();

        try {
            // 参数验证
            if (request.getLibraryId() == null) {
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                result.addErrorMessage("libraryId 不能为空");
                return result;
            }
            if (request.getListId() == null) {
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                result.addErrorMessage("listId 不能为空");
                return result;
            }

            log.info("获取图书馆书单详情: libraryId={}, listId={}, booksLimit={}", 
                     request.getLibraryId(), request.getListId(), request.getBooksLimit());

            // 获取指定书单
            LibBookList bookList = libBookListService.getBookListByLibraryIdAndListId(
                    request.getLibraryId(), request.getListId());

            if (bookList == null) {
                result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
                result.addErrorMessage("书单不存在或不可访问");
                return result;
            }

            // 构建响应数据
            RestLibraryBookList listInfo = new RestLibraryBookList();
            listInfo.setId(bookList.getLibListId());
            listInfo.setName(bookList.getListName());
            listInfo.setNameTc(bookList.getListNameTc());
            listInfo.setNameEn(bookList.getListNameEn());
            listInfo.setBannerUrl(bookList.getBannerUrl());
            listInfo.setListAbstract(bookList.getListAbstract());
            listInfo.setShowOrder(bookList.getShowOrder());

            // 获取书单中的书籍详情
            List<RestBook> books = libBookListService.parseAndGetRestBooks(
                    bookList.getBookIdText(), 
                    request.getLibraryId(), 
                    request.getBooksLimit()
            );
            
            listInfo.setBooks(books);
            listInfo.setBookCount(books.size());
            
            result.setResult(listInfo);
            result.setSuccessMessage(ReturnInfo.SUCCESS_QUERY);

        } catch (Exception e) {
            log.error("获取图书馆书单详情失败: libraryId={}, listId={}", 
                     request.getLibraryId(), request.getListId(), e);
            result.addErrorMessage("系统内部错误");
            result.addErrorMessage("获取书单详情失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 处理JSON解析错误
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ExecuteResult<String> handleJsonParseError(HttpMessageNotReadableException e) {
        ExecuteResult<String> result = new ExecuteResult<>();
        
        log.error("JSON解析错误", e);
        
        String errorMessage = "请求参数格式错误";
        if (e.getCause() != null && e.getCause().getMessage() != null) {
            String causeMessage = e.getCause().getMessage();
            if (causeMessage.contains("Unexpected character")) {
                errorMessage = "JSON格式错误：请检查是否有多余的逗号、空值或格式问题";
            } else if (causeMessage.contains("expected a value")) {
                errorMessage = "JSON格式错误：缺少必要的值，请检查字段是否完整";
            }
        }
        
        result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
        result.addErrorMessage("请确保请求体是有效的JSON格式，例如：{\"libraryId\": 1, \"libBookId\": 1}");
        
        return result;
    }
}