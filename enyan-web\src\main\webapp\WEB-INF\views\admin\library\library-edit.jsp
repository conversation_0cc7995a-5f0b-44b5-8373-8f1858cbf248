<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="csrf-token" content="${_csrf.token}">
    <title>图书馆编辑 - <spring:message code="main.title"/></title>
    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <script src="<c:url value='/js/jquery.min.js'/>"></script>
    <script>
        var libId = <c:choose><c:when test="${not empty libraryId}">'${libraryId}'</c:when><c:otherwise>null</c:otherwise></c:choose>;
        
        function checkKeyUnique(){
            var k = $('#key').val(); if (!k) return;
            $.get('<c:url value="/admin/library/api/libraries/check-key"/>', {key:k, excludeId: libId||''}).done(function(resp){
                if (resp && resp.data && resp.data.exists){
                    $('#keyHelp').text('Key已存在').css('color','#e67e22');
                } else { $('#keyHelp').text(''); }
            });
        }
        function save(){
            var params = {
                name: $('#name').val(),
                key: $('#key').val(),
                description: $('#description').val(),
                max_borrow_books: $('#maxBorrowBooks').val(),
                default_loan_days: $('#defaultLoanDays').val()
            };
            if ($('#status').length){ params.status = $('#status').val(); }
            if ($('#remark').length){ params.remark = $('#remark').val(); }
            if (libId) { delete params.key; }
            if (typeof window.__isTopAdmin !== 'undefined' && !window.__isTopAdmin) {
                // 分管理员不允许修改名称
                delete params.name;
            }
            var url = '<c:url value="/admin/library/api/libraries"/>' + (libId? ('/'+libId): '');
            $.jpost(url, params).then(function(resp){
                if (resp && (resp.code==='T'||resp.code===200||resp.code===0)){
                    showNotification('保存成功','success');
                    window.location.href = '<c:url value="/admin/library/libraries"/>';
                } else {
                    showNotification(resp.message||'保存失败','error');
                }
            }).catch(function(){ showNotification('网络错误','error'); });
        }
        function toggleStatus(){
            if (!libId) return;
            $.jpost('<c:url value="/admin/library/api/libraries"/>/'+ libId +'/toggle', {}).then(function(resp){
                if (resp && (resp.code==='T'||resp.code===200||resp.code===0)){
                    showNotification('切换成功','success');
                    window.location.reload();
                }else{showNotification(resp.message||'操作失败','error');}
            }).catch(function(){showNotification('网络错误','error');});
        }
        function del(){
            if (!libId) return; if (!confirm('确定删除该图书馆吗？')) return;
            $.jpost('<c:url value="/admin/library/api/libraries"/>/'+ libId +'/delete', {}).then(function(resp){
                if (resp && (resp.code==='T'||resp.code===200||resp.code===0)){
                    showNotification('删除成功','success');
                    window.location.href = '<c:url value="/admin/library/libraries"/>';
                }else{showNotification(resp.message||'删除失败','error');}
            }).catch(function(){showNotification('网络错误','error');});
        }

        function showNotification(message, type) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
                message +
                '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                '<span aria-hidden="true">&times;</span>' +
                '</button>' +
                '</div>');

            $('body').append(notification);

            // 自动移除通知
            setTimeout(function() {
                notification.alert('close');
            }, 3000);
        }
    </script>
    <sec:authorize access="hasRole('ADMIN')"><script>window.__isAdmin = true;</script></sec:authorize>
    <sec:authorize access="!hasRole('ADMIN')"><script>window.__isAdmin = false;</script></sec:authorize>
    <!-- 顶级管理员（系统/运营）标识 -->
    <sec:authorize access="hasAnyRole('ADMIN','OPERATION')"><script>window.__isTopAdmin = true;</script></sec:authorize>
    <sec:authorize access="!hasAnyRole('ADMIN','OPERATION')"><script>window.__isTopAdmin = false;</script></sec:authorize>
</head>
<body class="sticky-header">
<section>
    <jsp:include page="../adminLeft.jsp"/>
    <div class="main-content">
        <div class="header-section">
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <jsp:include page="header_library_switcher.jsp"/>
        </div>
        <div class="page-heading">
            <h3>图书馆编辑</h3>
            <ul class="breadcrumb">
                <li><a href="#"><spring:message code="menu.index"/></a></li>
                <li><a href="<c:url value='/admin/library' />">图书馆后台</a></li>
                <li class="active">编辑</li>
            </ul>
        </div>
        <div class="wrapper">
            <section class="panel">
                <header class="panel-heading">基本信息</header>
                <div class="panel-body">
                    <div class="form-horizontal">
                        <sec:authorize access="hasAnyRole('ADMIN','OPERATION')">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">图书馆ID</label>
                                <div class="col-sm-6">
                                    <input id="libraryId" type="text" class="form-control" 
                                           <c:choose>
                                               <c:when test="${empty library}">placeholder="系统自动生成"</c:when>
                                               <c:otherwise>value="<c:out value='${library.libraryId}'/>"</c:otherwise>
                                           </c:choose> disabled>
                                </div>
                            </div>
                        </sec:authorize>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">名称 <span class="text-danger">*</span></label>
                            <div class="col-sm-6">
                                <input id="name" type="text" class="form-control" maxlength="255" 
                                       <c:choose>
                                           <c:when test="${not empty library.name}">value="<c:out value='${library.name}'/>"</c:when>
                                           <c:otherwise>placeholder="请输入图书馆名称，如：T学院图书馆"</c:otherwise>
                                       </c:choose>
                                       <sec:authorize access="!hasAnyRole('ADMIN','OPERATION')">readonly</sec:authorize>>
                            </div>
                        </div>
                        <sec:authorize access="hasAnyRole('ADMIN','OPERATION')">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">标识码 <span class="text-danger">*</span></label>
                                <div class="col-sm-6">
                                    <input id="key" type="text" class="form-control" maxlength="50" 
                                           <c:choose>
                                               <c:when test="${not empty library.key}">value="<c:out value='${library.key}'/>" disabled</c:when>
                                               <c:otherwise>placeholder="请输入3-50个字符的小写字母、数字、下划线或连字符"</c:otherwise>
                                           </c:choose>>
                                    <div id="keyHelp" class="help-block"></div>
                                </div>
                            </div>
                        </sec:authorize>
                        <c:if test="${not empty library}">
                            <fmt:formatDate value="${library.createdAt}" pattern="yyyy-MM-dd" var="createdDateStr"/>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">创建日期</label>
                                <div class="col-sm-3">
                                    <input id="createdAt" type="text" class="form-control" value="${createdDateStr}" disabled>
                                </div>
                            </div>
                        </c:if>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">状态</label>
                            <div class="col-sm-3">
                                <select id="status" class="form-control">
                                    <option value="1" <c:if test="${library.status == 1}">selected</c:if>>启用</option>
                                    <option value="0" <c:if test="${library.status == 0}">selected</c:if>>禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">描述</label>
                            <div class="col-sm-8">
                                <textarea id="description" class="form-control" rows="4" maxlength="2000" 
                                          <c:if test="${empty library.description}">placeholder="请输入图书馆描述（可选）"</c:if>><c:if test="${not empty library.description}"><c:out value="${library.description}"/></c:if></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">借阅上限</label>
                            <div class="col-sm-3">
                                <input id="maxBorrowBooks" type="number" min="1" max="100" class="form-control" 
                                       <c:choose>
                                           <c:when test="${not empty library.maxBorrowBooks}">value="${library.maxBorrowBooks}"</c:when>
                                           <c:otherwise>placeholder="请输入借阅上限（1-100本）"</c:otherwise>
                                       </c:choose>>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">默认借期(天)</label>
                            <div class="col-sm-3">
                                <input id="defaultLoanDays" type="number" min="1" max="90" class="form-control" 
                                       <c:choose>
                                           <c:when test="${not empty library.defaultLoanDays}">value="${library.defaultLoanDays}"</c:when>
                                           <c:otherwise>placeholder="请输入默认借期（1-90天）"</c:otherwise>
                                       </c:choose>>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">备注</label>
                            <div class="col-sm-8">
                                <textarea id="remark" class="form-control" rows="3" maxlength="500" 
                                          <c:if test="${empty library.remark}">placeholder="请输入备注（可选）"</c:if>><c:if test="${not empty library.remark}"><c:out value="${library.remark}"/></c:if></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-10">
                                <button class="btn btn-primary" onclick="save()">保存</button>
                                <button class="btn btn-warning" onclick="toggleStatus()" <c:if test='${empty library}'>disabled</c:if>>启用/禁用</button>
                                <button class="btn btn-danger" onclick="del()" <c:if test='${empty library}'>disabled</c:if>>删除</button>
                                <a class="btn btn-default" href="<c:url value='/admin/library/libraries'/>">返回列表</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <jsp:include page="../footer_show.jsp"/>
    </div>
</section>

<!-- 引入必要的JavaScript -->
<jsp:include page="../footer_js.jsp"/>

<script>
// 非顶级管理员（系统/运营）隐藏/禁用敏感项
$(function(){
    if (typeof window.__isTopAdmin !== 'undefined' && !window.__isTopAdmin){
        // 隐藏状态、备注、启停、删除
        if ($('#status').length){ $('#status').closest('.form-group').remove(); }
        if ($('#remark').length){ $('#remark').closest('.form-group').remove(); }
        $("button[onclick='toggleStatus()']").remove();
        $("button[onclick='del()']").remove();
        // 隐藏ID与标识码（兜底）
        if ($('#libraryId').length){ $('#libraryId').closest('.form-group').remove(); }
        if ($('#key').length){ $('#key').closest('.form-group').remove(); }
        // 禁用名称编辑
        $('#name').prop('readonly', true);
    }
});
</script>

</body>
</html>
