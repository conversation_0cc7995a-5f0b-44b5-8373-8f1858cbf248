<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>

<%@ page import="com.aaron.api.constant.InterfaceContant" %>
<!-- left side start-->
<div class="left-side sticky-left-side">

  <!--logo and iconic logo start-->
  <div class="logo">
    <a href="/admin"><img src="<c:url value='/statics/images/logo.png' />" alt=""></a>
  </div>

  <div class="logo-icon text-center">
    <a href="/admin"><img src="<c:url value='/statics/images/logo_icon.png' />" alt=""></a>
  </div>
  <!--logo and iconic logo end-->

  <div class="left-side-inner">

    <!-- visible to small devices only -->
    <%--<div class="visible-xs hidden-sm hidden-md hidden-lg">
      <div class="media logged-user">
        <img alt="" src="<c:url value='/statics/images/photos/user-avatar.png' />" class="media-object">
        <div class="media-body">
          <h4><a href="#">John Doe</a></h4>
          <span>"Hello There..."</span>
        </div>
      </div>

      <h5 class="left-nav-title">Account Information</h5>
      <ul class="nav nav-pills nav-stacked custom-nav">
        <li><a href="#"><i class="fa fa-user"></i> <span>Profile</span></a></li>
        <li><a href="#"><i class="fa fa-cog"></i> <span>Settings</span></a></li>
        <li><a href="#"><i class="fa fa-sign-out"></i> <span>Sign Out</span></a></li>
      </ul>
    </div>--%>

    <!--sidebar nav start-->
<sec:authorize access="isAuthenticated() or isRememberMe()">
    <sec:authentication var="user" property="principal" />
    <ul class="nav nav-pills nav-stacked custom-nav">

      <li><a href="#"><i class="fa fa-home"></i> <span><spring:message code="menu.index"/> </span></a></li>

      <sec:authorize access="hasRole('ADMIN')">
          <jsp:include page="adminLeft2.jsp"/>
      </sec:authorize>
      <sec:authorize access="hasRole('FINANCE')">
          <jsp:include page="../finance/adminLeft.jsp"/>
      </sec:authorize>
      <sec:authorize access="hasRole('OPERATION')">
          <jsp:include page="../operation/adminLeft.jsp"/>
      </sec:authorize>
      <sec:authorize access="hasRole('VENDOR')">
          <jsp:include page="../vendor/adminLeft.jsp"/>
      </sec:authorize>
      <sec:authorize access="hasAnyRole('LIBRARIAN','ADMIN')">
          <jsp:include page="library/adminLeft.jsp"/>
      </sec:authorize>
      <li><a href="<c:url value="/logout" />"><i class="fa fa-sign-in"></i> <span><spring:message code="info.logout"/> </span></a></li>

    </ul>
</sec:authorize>

    <!--sidebar nav end-->

  </div>
</div>
<!-- left side end-->

