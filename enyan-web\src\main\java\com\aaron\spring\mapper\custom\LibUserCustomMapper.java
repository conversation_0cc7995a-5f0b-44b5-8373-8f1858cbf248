package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.LibLibrary;
import com.aaron.spring.model.view.LibLibrarianView;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LibUserCustomMapper {

    /**
     * 分页查询图书馆管理员
     * @param keyword 模糊匹配：邮箱/图书馆名称；可为空
     * @param offset 偏移量
     * @param limit 数量
     */
    List<LibLibrarianView> selectLibrarians(@Param("keyword") String keyword,
                                            @Param("offset") int offset,
                                            @Param("limit") int limit);

    /** 统计总数 */
    int countLibrarians(@Param("keyword") String keyword);

    /**
     * 分页查询图书馆管理员（限制在指定用户可管理的图书馆范围内）
     */
    List<LibLibrarianView> selectLibrariansByUserEmail(@Param("keyword") String keyword,
                                                       @Param("userEmail") String userEmail,
                                                       @Param("offset") int offset,
                                                       @Param("limit") int limit);

    /**
     * 统计管理员数量（限制在指定用户可管理的图书馆范围内）
     */
    int countLibrariansByUserEmail(@Param("keyword") String keyword,
                                   @Param("userEmail") String userEmail);

    /**
     * 按用户邮箱查询其可管理的已启用图书馆列表（基于 lib_user 确认关系）
     */
    List<LibLibrary> selectEnabledLibrariesByUserEmail(@Param("userEmail") String userEmail);

    /**
     * Query confirmed librarians by library id
     */
    List<LibLibrarianView> selectLibrariansByLibraryId(@Param("libraryId") Long libraryId);

    /**
     * 查询单个图书馆管理员详细信息
     */
    LibLibrarianView selectLibrarianDetail(@Param("libUserId") Long libUserId);
}
