package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.LibLibrary;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: <PERSON>
 * @Description: 图书馆前端展示模型
 * @Date: Created in  2025/1/17
 * @Modified By:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(description = "图书馆信息")
public class RestLibrary extends RestBaseDTO {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "图书馆ID", example = "1")
    private Long libraryId;
    
    @ApiModelProperty(value = "图书馆名称", example = "中央图书馆")
    private String name;
    
    @ApiModelProperty(value = "图书馆描述", example = "这是一个综合性图书馆")
    private String description;
    
    @ApiModelProperty(value = "图书馆唯一标识", example = "central_lib")
    private String key;
    
    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "备注信息", example = "备注", hidden = true)
    @JsonIgnore
    private String remark;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
    
    /**
     * 从LibLibrary模型初始化
     */
    public void initFrom(LibLibrary libLibrary) {
        if (libLibrary != null) {
            this.libraryId = libLibrary.getLibraryId();
            this.name = libLibrary.getName();
            this.description = libLibrary.getDescription();
            this.key = libLibrary.getKey();
            this.status = libLibrary.getStatus();
            this.remark = libLibrary.getRemark();
            this.createdAt = libLibrary.getCreatedAt();
        }
    }
}